from google.colab import files

uploaded = files.upload()

for fn in uploaded.keys():
  print('User uploaded file "{name}" with length {length} bytes'.format(
      name=fn, length=len(uploaded[fn])))

!mkdir -p ~/.kaggle/ && mv kaggle.json ~/.kaggle/ && chmod 600 ~/.kaggle/kaggle.json



!kaggle competitions download -c aptos2019-blindness-detection

from zipfile import ZipFile
file_name = "/content/aptos2019-blindness-detection.zip"

with ZipFile(file_name, 'r') as zip:
  zip.extractall()
  print('done')

from google.colab import drive
drive.mount('/content/drive')


!pip install tensorflow keras numpy matplotlib efficientnet


import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Dense, GlobalAveragePooling2D
from tensorflow.keras.preprocessing.image import ImageDataGenerator
import efficientnet.tfkeras as efn
import os
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import confusion_matrix
import seaborn as sns


# Set paths
dataset_dir = "/content/train_images/"  # Update if needed
csv_file = "/content/train.csv"  # Update if needed

# Load CSV file containing image labels
df = pd.read_csv(csv_file)

# Convert filenames to full paths
df["id_code"] = df["id_code"].apply(lambda x: os.path.join(dataset_dir, x + ".png"))

# Rename columns for easier reference
df.columns = ["image_path", "label"]

# Convert labels to string (needed for ImageDataGenerator)
df["label"] = df["label"].astype(str)

# Split dataset into Training (80%) and Validation (20%)
train_df, val_df = train_test_split(df, test_size=0.2, stratify=df["label"], random_state=42)

# Define ImageDataGenerators for data augmentation
train_datagen = ImageDataGenerator(
    rescale=1.0 / 255.0,   # Normalize pixel values
    rotation_range=20,     # Random rotation
    width_shift_range=0.1, # Horizontal shift
    height_shift_range=0.1,
    zoom_range=0.2,        # Random zoom
    horizontal_flip=True   # Random horizontal flip
)

val_datagen = ImageDataGenerator(rescale=1.0 / 255.0)  # Only normalize

# Create ImageDataGenerators
train_generator = train_datagen.flow_from_dataframe(
    train_df,
    x_col="image_path",
    y_col="label",
    target_size=(224, 224),  # Resize to EfficientNetB0's input size
    batch_size=32,
    class_mode="categorical"
)

val_generator = val_datagen.flow_from_dataframe(
    val_df,
    x_col="image_path",
    y_col="label",
    target_size=(224, 224),
    batch_size=32,
    class_mode="categorical"
)

print("✅ Dataset loaded and preprocessed successfully!")


# Load EfficientNetB0 model without the top layer
base_model = efn.EfficientNetB0(weights="imagenet", include_top=False)

# Freeze all layers (so they don’t get trained)
for layer in base_model.layers:
    layer.trainable = False

# Get the number of classes using class_indices
num_classes = len(train_generator.class_indices)

# Add new layers on top
x = GlobalAveragePooling2D()(base_model.output)
x = Dense(512, activation='relu')(x)
x = Dense(256, activation='relu')(x)
output_layer = Dense(num_classes, activation='softmax')(x) # Use num_classes here

# Create final model
model = Model(inputs=base_model.input, outputs=output_layer)


model.compile(optimizer='adam',
              loss='categorical_crossentropy',
              metrics=['accuracy'])


history = model.fit(
    train_generator,
    validation_data=val_generator,
    epochs=5,  # Change if needed
    verbose=1
)


# Evaluate model performance
loss, accuracy = model.evaluate(val_generator)
print(f"Validation Accuracy: {accuracy*100:.2f}%")

# Save trained model
model.save("/content/drive/MyDrive/model.h5")


import matplotlib.pyplot as plt

# Plot training accuracy and validation accuracy
plt.plot(history.history['accuracy'], label='Train Accuracy')
plt.plot(history.history['val_accuracy'], label='Val Accuracy')
plt.xlabel('Epochs')
plt.ylabel('Accuracy')
plt.legend()
plt.title('Training and Validation Accuracy')
plt.show()

# Plot training loss and validation loss
plt.plot(history.history['loss'], label='Train Loss')
plt.plot(history.history['val_loss'], label='Val Loss')
plt.xlabel('Epochs')
plt.ylabel('Loss')
plt.legend()
plt.title('Training and Validation Loss')
plt.show()


# Predict on validation data
Y_pred = model.predict(val_generator)
y_pred = np.argmax(Y_pred, axis=1)

# Get true labels
Y_true = val_generator.classes

# Compute the confusion matrix
cm = confusion_matrix(Y_true, y_pred)

# Plot the confusion matrix using seaborn
plt.figure(figsize=(10, 8))
sns.heatmap(cm, annot=True, fmt="d", cmap="Blues",
            xticklabels=list(train_generator.class_indices.keys()),
            yticklabels=list(train_generator.class_indices.keys()))
plt.xlabel("Predicted Labels")
plt.ylabel("True Labels")
plt.title("Confusion Matrix")
plt.show()
