{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 90}, "id": "qOCeT9FKSAl2", "outputId": "b3060a68-d70e-4317-977c-d13b49656e0b"}, "outputs": [{"data": {"text/html": ["\n", "     <input type=\"file\" id=\"files-c67a104c-057f-46b6-a6d8-711331e251a4\" name=\"files[]\" multiple disabled\n", "        style=\"border:none\" />\n", "     <output id=\"result-c67a104c-057f-46b6-a6d8-711331e251a4\">\n", "      Upload widget is only available when the cell has been executed in the\n", "      current browser session. Please rerun this cell to enable.\n", "      </output>\n", "      <script>// Copyright 2017 Google LLC\n", "//\n", "// Licensed under the Apache License, Version 2.0 (the \"License\");\n", "// you may not use this file except in compliance with the License.\n", "// You may obtain a copy of the License at\n", "//\n", "//      http://www.apache.org/licenses/LICENSE-2.0\n", "//\n", "// Unless required by applicable law or agreed to in writing, software\n", "// distributed under the License is distributed on an \"AS IS\" BASIS,\n", "// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n", "// See the License for the specific language governing permissions and\n", "// limitations under the License.\n", "\n", "/**\n", " * @fileoverview Helpers for google.colab Python module.\n", " */\n", "(function(scope) {\n", "function span(text, styleAttributes = {}) {\n", "  const element = document.createElement('span');\n", "  element.textContent = text;\n", "  for (const key of Object.keys(styleAttributes)) {\n", "    element.style[key] = styleAttributes[key];\n", "  }\n", "  return element;\n", "}\n", "\n", "// Max number of bytes which will be uploaded at a time.\n", "const MAX_PAYLOAD_SIZE = 100 * 1024;\n", "\n", "function _uploadFiles(inputId, outputId) {\n", "  const steps = uploadFilesStep(inputId, outputId);\n", "  const outputElement = document.getElementById(outputId);\n", "  // Cache steps on the outputElement to make it available for the next call\n", "  // to uploadFilesContinue from Python.\n", "  outputElement.steps = steps;\n", "\n", "  return _uploadFilesContinue(outputId);\n", "}\n", "\n", "// This is roughly an async generator (not supported in the browser yet),\n", "// where there are multiple asynchronous steps and the Python side is going\n", "// to poll for completion of each step.\n", "// This uses a Promise to block the python side on completion of each step,\n", "// then passes the result of the previous step as the input to the next step.\n", "function _uploadFilesContinue(outputId) {\n", "  const outputElement = document.getElementById(outputId);\n", "  const steps = outputElement.steps;\n", "\n", "  const next = steps.next(outputElement.lastPromiseValue);\n", "  return Promise.resolve(next.value.promise).then((value) => {\n", "    // Cache the last promise value to make it available to the next\n", "    // step of the generator.\n", "    outputElement.lastPromiseValue = value;\n", "    return next.value.response;\n", "  });\n", "}\n", "\n", "/**\n", " * Generator function which is called between each async step of the upload\n", " * process.\n", " * @param {string} inputId Element ID of the input file picker element.\n", " * @param {string} outputId Element ID of the output display.\n", " * @return {!Iterable<!Object>} Iterable of next steps.\n", " */\n", "function* uploadFilesStep(inputId, outputId) {\n", "  const inputElement = document.getElementById(inputId);\n", "  inputElement.disabled = false;\n", "\n", "  const outputElement = document.getElementById(outputId);\n", "  outputElement.innerHTML = '';\n", "\n", "  const pickedPromise = new Promise((resolve) => {\n", "    inputElement.addEventListener('change', (e) => {\n", "      resolve(e.target.files);\n", "    });\n", "  });\n", "\n", "  const cancel = document.createElement('button');\n", "  inputElement.parentElement.appendChild(cancel);\n", "  cancel.textContent = 'Cancel upload';\n", "  const cancelPromise = new Promise((resolve) => {\n", "    cancel.onclick = () => {\n", "      resolve(null);\n", "    };\n", "  });\n", "\n", "  // Wait for the user to pick the files.\n", "  const files = yield {\n", "    promise: Promise.race([pickedPromise, cancelPromise]),\n", "    response: {\n", "      action: 'starting',\n", "    }\n", "  };\n", "\n", "  cancel.remove();\n", "\n", "  // Disable the input element since further picks are not allowed.\n", "  inputElement.disabled = true;\n", "\n", "  if (!files) {\n", "    return {\n", "      response: {\n", "        action: 'complete',\n", "      }\n", "    };\n", "  }\n", "\n", "  for (const file of files) {\n", "    const li = document.createElement('li');\n", "    li.append(span(file.name, {fontWeight: 'bold'}));\n", "    li.append(span(\n", "        `(${file.type || 'n/a'}) - ${file.size} bytes, ` +\n", "        `last modified: ${\n", "            file.lastModifiedDate ? file.lastModifiedDate.toLocaleDateString() :\n", "                                    'n/a'} - `));\n", "    const percent = span('0% done');\n", "    li.append<PERSON><PERSON>d(percent);\n", "\n", "    outputElement.appendChild(li);\n", "\n", "    const fileDataPromise = new Promise((resolve) => {\n", "      const reader = new FileReader();\n", "      reader.onload = (e) => {\n", "        resolve(e.target.result);\n", "      };\n", "      reader.readAsArrayBuffer(file);\n", "    });\n", "    // Wait for the data to be ready.\n", "    let fileData = yield {\n", "      promise: fileDataPromise,\n", "      response: {\n", "        action: 'continue',\n", "      }\n", "    };\n", "\n", "    // Use a chunked sending to avoid message size limits. See b/62115660.\n", "    let position = 0;\n", "    do {\n", "      const length = Math.min(fileData.byteLength - position, MAX_PAYLOAD_SIZE);\n", "      const chunk = new Uint8Array(fileData, position, length);\n", "      position += length;\n", "\n", "      const base64 = btoa(String.fromCharCode.apply(null, chunk));\n", "      yield {\n", "        response: {\n", "          action: 'append',\n", "          file: file.name,\n", "          data: base64,\n", "        },\n", "      };\n", "\n", "      let percentDone = fileData.byteLength === 0 ?\n", "          100 :\n", "          Math.round((position / fileData.byteLength) * 100);\n", "      percent.textContent = `${percentDone}% done`;\n", "\n", "    } while (position < fileData.byteLength);\n", "  }\n", "\n", "  // All done.\n", "  yield {\n", "    response: {\n", "      action: 'complete',\n", "    }\n", "  };\n", "}\n", "\n", "scope.google = scope.google || {};\n", "scope.google.colab = scope.google.colab || {};\n", "scope.google.colab._files = {\n", "  _uploadFiles,\n", "  _uploadFilesContinue,\n", "};\n", "})(self);\n", "</script> "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Saving kaggle.json to kaggle.json\n", "User uploaded file \"kaggle.json\" with length 68 bytes\n"]}], "source": ["from google.colab import files\n", "\n", "uploaded = files.upload()\n", "\n", "for fn in uploaded.keys():\n", "  print('User uploaded file \"{name}\" with length {length} bytes'.format(\n", "      name=fn, length=len(uploaded[fn])))\n", "\n", "!mkdir -p ~/.kaggle/ && mv kaggle.json ~/.kaggle/ && chmod 600 ~/.kaggle/kaggle.json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HlsZaPKdS2wM", "outputId": "27f0b66f-6939-4b1d-8000-3eb99f44ce2b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading aptos2019-blindness-detection.zip to /content\n", "100% 9.50G/9.51G [00:59<00:00, 145MB/s]\n", "100% 9.51G/9.51G [00:59<00:00, 173MB/s]\n"]}], "source": ["!kaggle competitions download -c aptos2019-blindness-detection"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SrAygfsQVdrG", "outputId": "9d2a4c0c-219d-428b-985f-e00376248e07"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["done\n"]}], "source": ["from zipfile import ZipFile\n", "file_name = \"/content/aptos2019-blindness-detection.zip\"\n", "\n", "with ZipFile(file_name, 'r') as zip:\n", "  zip.extractall()\n", "  print('done')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "S5bANdVYJZ7N", "outputId": "8a051838-0a73-408b-df80-6f4e5de89a6d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "Bp83pMTkJjiA", "outputId": "b6158c65-dc91-427e-9111-038fd199c12f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: tensorflow in /usr/local/lib/python3.11/dist-packages (2.18.0)\n", "Requirement already satisfied: keras in /usr/local/lib/python3.11/dist-packages (3.8.0)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (1.26.4)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (3.10.0)\n", "Collecting efficientnet\n", "  Downloading efficientnet-1.1.1-py3-none-any.whl.metadata (6.4 kB)\n", "Requirement already satisfied: absl-py>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.4.0)\n", "Requirement already satisfied: astunparse>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.6.3)\n", "Requirement already satisfied: flatbuffers>=24.3.25 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (25.2.10)\n", "Requirement already satisfied: gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (0.6.0)\n", "Requirement already satisfied: google-pasta>=0.1.1 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (0.2.0)\n", "Requirement already satisfied: libclang>=13.0.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (18.1.1)\n", "Requirement already satisfied: opt-einsum>=2.3.2 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (3.4.0)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from tensorflow) (24.2)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<6.0.0dev,>=3.20.3 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (4.25.6)\n", "Requirement already satisfied: requests<3,>=2.21.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (2.32.3)\n", "Requirement already satisfied: setuptools in /usr/local/lib/python3.11/dist-packages (from tensorflow) (75.1.0)\n", "Requirement already satisfied: six>=1.12.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.17.0)\n", "Requirement already satisfied: termcolor>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (2.5.0)\n", "Requirement already satisfied: typing-extensions>=3.6.6 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (4.12.2)\n", "Requirement already satisfied: wrapt>=1.11.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.17.2)\n", "Requirement already satisfied: grpcio<2.0,>=1.24.3 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.70.0)\n", "Requirement already satisfied: tensorboard<2.19,>=2.18 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (2.18.0)\n", "Requirement already satisfied: h5py>=3.11.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (3.12.1)\n", "Requirement already satisfied: ml-dtypes<0.5.0,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (0.4.1)\n", "Requirement already satisfied: tensorflow-io-gcs-filesystem>=0.23.1 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (0.37.1)\n", "Requirement already satisfied: rich in /usr/local/lib/python3.11/dist-packages (from keras) (13.9.4)\n", "Requirement already satisfied: namex in /usr/local/lib/python3.11/dist-packages (from keras) (0.0.8)\n", "Requirement already satisfied: optree in /usr/local/lib/python3.11/dist-packages (from keras) (0.14.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (4.56.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (11.1.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (3.2.1)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (2.8.2)\n", "Collecting keras-applications<=1.0.8,>=1.0.7 (from efficientnet)\n", "  Downloading Keras_Applications-1.0.8-py3-none-any.whl.metadata (1.7 kB)\n", "Requirement already satisfied: scikit-image in /usr/local/lib/python3.11/dist-packages (from efficientnet) (0.25.1)\n", "Requirement already satisfied: wheel<1.0,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from astunparse>=1.6.0->tensorflow) (0.45.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.21.0->tensorflow) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.21.0->tensorflow) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.21.0->tensorflow) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.21.0->tensorflow) (2025.1.31)\n", "Requirement already satisfied: markdown>=2.6.8 in /usr/local/lib/python3.11/dist-packages (from tensorboard<2.19,>=2.18->tensorflow) (3.7)\n", "Requirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in /usr/local/lib/python3.11/dist-packages (from tensorboard<2.19,>=2.18->tensorflow) (0.7.2)\n", "Requirement already satisfied: werkzeug>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from tensorboard<2.19,>=2.18->tensorflow) (3.1.3)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich->keras) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.11/dist-packages (from rich->keras) (2.18.0)\n", "Requirement already satisfied: scipy>=1.11.2 in /usr/local/lib/python3.11/dist-packages (from scikit-image->efficientnet) (1.13.1)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.11/dist-packages (from scikit-image->efficientnet) (3.4.2)\n", "Requirement already satisfied: imageio!=2.35.0,>=2.33 in /usr/local/lib/python3.11/dist-packages (from scikit-image->efficientnet) (2.37.0)\n", "Requirement already satisfied: tifffile>=2022.8.12 in /usr/local/lib/python3.11/dist-packages (from scikit-image->efficientnet) (2025.1.10)\n", "Requirement already satisfied: lazy-loader>=0.4 in /usr/local/lib/python3.11/dist-packages (from scikit-image->efficientnet) (0.4)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich->keras) (0.1.2)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /usr/local/lib/python3.11/dist-packages (from werkzeug>=1.0.1->tensorboard<2.19,>=2.18->tensorflow) (3.0.2)\n", "Downloading efficientnet-1.1.1-py3-none-any.whl (18 kB)\n", "Downloading Keras_Applications-1.0.8-py3-none-any.whl (50 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.7/50.7 kB\u001b[0m \u001b[31m3.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: keras-applications, efficientnet\n", "Successfully installed efficientnet-1.1.1 keras-applications-1.0.8\n"]}], "source": ["!pip install tensorflow keras numpy matplotlib efficientnet\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "Pga-rLx2JwFD"}, "outputs": [], "source": ["import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras.models import Model\n", "from tensorflow.keras.layers import Dense, GlobalAveragePooling2D\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "import efficientnet.tfkeras as efn\n", "import os\n", "from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from sklearn.metrics import confusion_matrix\n", "import seaborn as sns\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6DgziU-CJ712", "outputId": "9f56f269-5a8e-445d-dea9-09036e97beb2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 2929 validated image filenames belonging to 5 classes.\n", "Found 733 validated image filenames belonging to 5 classes.\n", "✅ Dataset loaded and preprocessed successfully!\n"]}], "source": ["# Set paths\n", "dataset_dir = \"/content/train_images/\"  # Update if needed\n", "csv_file = \"/content/train.csv\"  # Update if needed\n", "\n", "# Load CSV file containing image labels\n", "df = pd.read_csv(csv_file)\n", "\n", "# Convert filenames to full paths\n", "df[\"id_code\"] = df[\"id_code\"].apply(lambda x: os.path.join(dataset_dir, x + \".png\"))\n", "\n", "# Rename columns for easier reference\n", "df.columns = [\"image_path\", \"label\"]\n", "\n", "# Convert labels to string (needed for ImageDataGenerator)\n", "df[\"label\"] = df[\"label\"].astype(str)\n", "\n", "# Split dataset into Training (80%) and Validation (20%)\n", "train_df, val_df = train_test_split(df, test_size=0.2, stratify=df[\"label\"], random_state=42)\n", "\n", "# Define ImageDataGenerators for data augmentation\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1.0 / 255.0,   # Normalize pixel values\n", "    rotation_range=20,     # Random rotation\n", "    width_shift_range=0.1, # Horizontal shift\n", "    height_shift_range=0.1,\n", "    zoom_range=0.2,        # Random zoom\n", "    horizontal_flip=True   # Random horizontal flip\n", ")\n", "\n", "val_datagen = ImageDataGenerator(rescale=1.0 / 255.0)  # Only normalize\n", "\n", "# Create ImageDataGenerators\n", "train_generator = train_datagen.flow_from_dataframe(\n", "    train_df,\n", "    x_col=\"image_path\",\n", "    y_col=\"label\",\n", "    target_size=(224, 224),  # Resize to EfficientNetB0's input size\n", "    batch_size=32,\n", "    class_mode=\"categorical\"\n", ")\n", "\n", "val_generator = val_datagen.flow_from_dataframe(\n", "    val_df,\n", "    x_col=\"image_path\",\n", "    y_col=\"label\",\n", "    target_size=(224, 224),\n", "    batch_size=32,\n", "    class_mode=\"categorical\"\n", ")\n", "\n", "print(\"✅ Dataset loaded and preprocessed successfully!\")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GaYsD2O0XFdJ", "outputId": "b75e200b-ed25-472b-9e0e-b72ff6d50603"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading data from https://github.com/Callidior/keras-applications/releases/download/efficientnet/efficientnet-b0_weights_tf_dim_ordering_tf_kernels_autoaugment_notop.h5\n", "\u001b[1m16804768/16804768\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 0us/step\n"]}], "source": ["# Load EfficientNetB0 model without the top layer\n", "base_model = efn.EfficientNetB0(weights=\"imagenet\", include_top=False)\n", "\n", "# Freeze all layers (so they don’t get trained)\n", "for layer in base_model.layers:\n", "    layer.trainable = False\n", "\n", "# Get the number of classes using class_indices\n", "num_classes = len(train_generator.class_indices)\n", "\n", "# Add new layers on top\n", "x = GlobalAveragePooling2D()(base_model.output)\n", "x = Dense(512, activation='relu')(x)\n", "x = Dense(256, activation='relu')(x)\n", "output_layer = Dense(num_classes, activation='softmax')(x) # Use num_classes here\n", "\n", "# Create final model\n", "model = Model(inputs=base_model.input, outputs=output_layer)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "7qK2MRkfXnQ2"}, "outputs": [], "source": ["model.compile(optimizer='adam',\n", "              loss='categorical_crossentropy',\n", "              metrics=['accuracy'])\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Owi726kRXlZi", "outputId": "f72e3549-5aa8-467b-c805-f34a5cfc2633"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/5\n", "\u001b[1m92/92\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m746s\u001b[0m 8s/step - accuracy: 0.7685 - loss: 0.6292 - val_accuracy: 0.7135 - val_loss: 0.8109\n", "Epoch 2/5\n", "\u001b[1m92/92\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m735s\u001b[0m 8s/step - accuracy: 0.7541 - loss: 0.6540 - val_accuracy: 0.7531 - val_loss: 0.6514\n", "Epoch 3/5\n", "\u001b[1m92/92\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m725s\u001b[0m 8s/step - accuracy: 0.7568 - loss: 0.6314 - val_accuracy: 0.7640 - val_loss: 0.6495\n", "Epoch 4/5\n", "\u001b[1m92/92\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m736s\u001b[0m 8s/step - accuracy: 0.7829 - loss: 0.5750 - val_accuracy: 0.7231 - val_loss: 0.7045\n", "Epoch 5/5\n", "\u001b[1m92/92\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m731s\u001b[0m 8s/step - accuracy: 0.7557 - loss: 0.6219 - val_accuracy: 0.7531 - val_loss: 0.6459\n"]}], "source": ["history = model.fit(\n", "    train_generator,\n", "    validation_data=val_generator,\n", "    epochs=5,  # Change if needed\n", "    verbose=1\n", ")\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mqpuK2cdX-Z6", "outputId": "8de162ff-977e-4e68-9337-7a47350be788"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m23/23\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m129s\u001b[0m 5s/step - accuracy: 0.7456 - loss: 0.6651\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Validation Accuracy: 75.31%\n"]}], "source": ["# Evaluate model performance\n", "loss, accuracy = model.evaluate(val_generator)\n", "print(f\"Validation Accuracy: {accuracy*100:.2f}%\")\n", "\n", "# Save trained model\n", "model.save(\"/content/drive/MyDrive/model.h5\")\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 927}, "id": "bR04lDQHX_uw", "outputId": "56221103-84b5-4336-b07b-ebd7fc558c41"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkAAAAHHCAYAAABXx+fLAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAaF5JREFUeJzt3Xd4FOXexvHvpjdSCCQECKH33kIRQUUpyhErINIEUUQU0fOqR6WoRzzqURQVEFFsCIJYjiIIKBa6hCpFEEjoPYSasjvvH5MsLAmQPtns/bmuvZidzM7+hk3IzVPmsRmGYSAiIiLiQbysLkBERESkuCkAiYiIiMdRABIRERGPowAkIiIiHkcBSERERDyOApCIiIh4HAUgERER8TgKQCIiIuJxFIBERETE4ygAiZQwAwcOpGrVqvl67dixY7HZbIVbUAmze/dubDYb06dPL/b3ttlsjB071vl8+vTp2Gw2du/efdXXVq1alYEDBxZqPQX5XhHxdApAIrlks9ly9ViyZInVpXq8Rx55BJvNxo4dOy57zDPPPIPNZmPDhg3FWFne7d+/n7Fjx7Ju3TqrS3HKCqGvvfaa1aWI5JuP1QWIuItPPvnE5fnHH3/MwoULs+2vV69egd5n6tSpOByOfL322Wef5amnnirQ+5cGffv2ZeLEicyYMYPRo0fneMznn39Oo0aNaNy4cb7fp1+/fvTu3Rt/f/98n+Nq9u/fz7hx46hatSpNmzZ1+VpBvldEPJ0CkEgu3XvvvS7PV6xYwcKFC7Ptv9TZs2cJCgrK9fv4+vrmqz4AHx8ffHz0Yx0fH0/NmjX5/PPPcwxAy5cvZ9euXbz88ssFeh9vb2+8vb0LdI6CKMj3ioinUxeYSCHq1KkTDRs2ZM2aNVx77bUEBQXxr3/9C4BvvvmGm2++mYoVK+Lv70+NGjV44YUXsNvtLue4dFzHxd0N7733HjVq1MDf359WrVqxevVql9fmNAbIZrPx8MMP8/XXX9OwYUP8/f1p0KAB8+fPz1b/kiVLaNmyJQEBAdSoUYMpU6bkelzRb7/9xl133UWVKlXw9/cnNjaWxx57jHPnzmW7vpCQEPbt20fPnj0JCQmhfPnyPPHEE9n+LpKTkxk4cCBhYWGEh4czYMAAkpOTr1oLmK1AW7duJSEhIdvXZsyYgc1mo0+fPqSlpTF69GhatGhBWFgYwcHBdOjQgZ9//vmq75HTGCDDMHjxxRepXLkyQUFBXHfddfz555/ZXnv8+HGeeOIJGjVqREhICKGhoXTr1o3169c7j1myZAmtWrUCYNCgQc5u1qzxTzmNATpz5gyPP/44sbGx+Pv7U6dOHV577TUMw3A5Li/fF/l1+PBhBg8eTHR0NAEBATRp0oSPPvoo23EzZ86kRYsWlClThtDQUBo1asSbb77p/Hp6ejrjxo2jVq1aBAQEEBkZyTXXXMPChQsLrVbxPPqvokghO3bsGN26daN3797ce++9REdHA+Yvy5CQEEaNGkVISAg//fQTo0ePJiUlhVdfffWq550xYwanTp3igQcewGaz8corr3D77bezc+fOq7YE/P7778ydO5eHHnqIMmXK8NZbb3HHHXeQlJREZGQkAGvXrqVr167ExMQwbtw47HY7zz//POXLl8/Vdc+ePZuzZ88ybNgwIiMjWbVqFRMnTmTv3r3Mnj3b5Vi73U6XLl2Ij4/ntddeY9GiRfz3v/+lRo0aDBs2DDCDxK233srvv//Ogw8+SL169fjqq68YMGBArurp27cv48aNY8aMGTRv3tzlvb/44gs6dOhAlSpVOHr0KO+//z59+vTh/vvv59SpU0ybNo0uXbqwatWqbN1OVzN69GhefPFFunfvTvfu3UlISOCmm24iLS3N5bidO3fy9ddfc9ddd1GtWjUOHTrElClT6NixI5s3b6ZixYrUq1eP559/ntGjRzN06FA6dOgAQLt27XJ8b8Mw+Mc//sHPP//M4MGDadq0KQsWLOCf//wn+/bt44033nA5PjffF/l17tw5OnXqxI4dO3j44YepVq0as2fPZuDAgSQnJ/Poo48CsHDhQvr06cMNN9zAf/7zHwC2bNnC0qVLnceMHTuW8ePHM2TIEFq3bk1KSgp//PEHCQkJ3HjjjQWqUzyYISL5Mnz4cOPSH6GOHTsagDF58uRsx589ezbbvgceeMAICgoyzp8/79w3YMAAIy4uzvl8165dBmBERkYax48fd+7/5ptvDMD43//+59w3ZsyYbDUBhp+fn7Fjxw7nvvXr1xuAMXHiROe+Hj16GEFBQca+ffuc+7Zv3274+PhkO2dOcrq+8ePHGzabzUhMTHS5PsB4/vnnXY5t1qyZ0aJFC+fzr7/+2gCMV155xbkvIyPD6NChgwEYH3744VVratWqlVG5cmXDbrc7982fP98AjClTpjjPmZqa6vK6EydOGNHR0cZ9993nsh8wxowZ43z+4YcfGoCxa9cuwzAM4/Dhw4afn59x8803Gw6Hw3ncv/71LwMwBgwY4Nx3/vx5l7oMw/ys/f39Xf5uVq9efdnrvfR7Jevv7MUXX3Q57s477zRsNpvL90Buvy9ykvU9+eqrr172mAkTJhiA8emnnzr3paWlGW3btjVCQkKMlJQUwzAM49FHHzVCQ0ONjIyMy56rSZMmxs0333zFmkTySl1gIoXM39+fQYMGZdsfGBjo3D516hRHjx6lQ4cOnD17lq1bt171vL169SIiIsL5PKs1YOfOnVd9befOnalRo4bzeePGjQkNDXW+1m63s2jRInr27EnFihWdx9WsWZNu3bpd9fzgen1nzpzh6NGjtGvXDsMwWLt2bbbjH3zwQZfnHTp0cLmWefPm4ePj42wRAnPMzYgRI3JVD5jjtvbu3cuvv/7q3Ddjxgz8/Py46667nOf08/MDwOFwcPz4cTIyMmjZsmWO3WdXsmjRItLS0hgxYoRLt+HIkSOzHevv74+Xl/lPsN1u59ixY4SEhFCnTp08v2+WefPm4e3tzSOPPOKy//HHH8cwDH744QeX/Vf7viiIefPmUaFCBfr06ePc5+vryyOPPMLp06f55ZdfAAgPD+fMmTNX7M4KDw/nzz//ZPv27QWuSySLApBIIatUqZLzF+rF/vzzT2677TbCwsIIDQ2lfPnyzgHUJ0+evOp5q1Sp4vI8KwydOHEiz6/Nen3Waw8fPsy5c+eoWbNmtuNy2peTpKQkBg4cSNmyZZ3jejp27Ahkv76AgIBsXWsX1wOQmJhITEwMISEhLsfVqVMnV/UA9O7dG29vb2bMmAHA+fPn+eqrr+jWrZtLmPzoo49o3Lixc3xJ+fLl+f7773P1uVwsMTERgFq1arnsL1++vMv7gRm23njjDWrVqoW/vz/lypWjfPnybNiwIc/ve/H7V6xYkTJlyrjsz5qZmFVflqt9XxREYmIitWrVcoa8y9Xy0EMPUbt2bbp160blypW57777so1Dev7550lOTqZ27do0atSIf/7znyX+9gVS8ikAiRSyi1tCsiQnJ9OxY0fWr1/P888/z//+9z8WLlzoHPOQm6nMl5ttZFwyuLWwX5sbdrudG2+8ke+//54nn3ySr7/+moULFzoH6156fcU1cyoqKoobb7yRL7/8kvT0dP73v/9x6tQp+vbt6zzm008/ZeDAgdSoUYNp06Yxf/58Fi5cyPXXX1+kU8xfeuklRo0axbXXXsunn37KggULWLhwIQ0aNCi2qe1F/X2RG1FRUaxbt45vv/3WOX6pW7duLmO9rr32Wv7++28++OADGjZsyPvvv0/z5s15//33i61OKX00CFqkGCxZsoRjx44xd+5crr32Wuf+Xbt2WVjVBVFRUQQEBOR448Ar3Uwwy8aNG/nrr7/46KOP6N+/v3N/QWbpxMXFsXjxYk6fPu3SCrRt27Y8nadv377Mnz+fH374gRkzZhAaGkqPHj2cX58zZw7Vq1dn7ty5Lt1WY8aMyVfNANu3b6d69erO/UeOHMnWqjJnzhyuu+46pk2b5rI/OTmZcuXKOZ/n5c7ecXFxLFq0iFOnTrm0AmV1sWbVVxzi4uLYsGEDDofDpRUop1r8/Pzo0aMHPXr0wOFw8NBDDzFlyhSee+45Zwtk2bJlGTRoEIMGDeL06dNce+21jB07liFDhhTbNUnpohYgkWKQ9T/ti/9nnZaWxrvvvmtVSS68vb3p3LkzX3/9Nfv373fu37FjR7ZxI5d7Pbhen2EYLlOZ86p79+5kZGQwadIk5z673c7EiRPzdJ6ePXsSFBTEu+++yw8//MDtt99OQEDAFWtfuXIly5cvz3PNnTt3xtfXl4kTJ7qcb8KECdmO9fb2ztbSMnv2bPbt2+eyLzg4GCBX0/+7d++O3W7n7bffdtn/xhtvYLPZcj2eqzB0796dgwcPMmvWLOe+jIwMJk6cSEhIiLN79NixYy6v8/Lyct6cMjU1NcdjQkJCqFmzpvPrIvmhFiCRYtCuXTsiIiIYMGCAc5mGTz75pFi7Gq5m7Nix/Pjjj7Rv355hw4Y5f5E2bNjwqssw1K1blxo1avDEE0+wb98+QkND+fLLLws0lqRHjx60b9+ep556it27d1O/fn3mzp2b5/ExISEh9OzZ0zkO6OLuL4BbbrmFuXPnctttt3HzzTeza9cuJk+eTP369Tl9+nSe3ivrfkbjx4/nlltuoXv37qxdu5YffvjBpVUn632ff/55Bg0aRLt27di4cSOfffaZS8sRQI0aNQgPD2fy5MmUKVOG4OBg4uPjqVatWrb379GjB9dddx3PPPMMu3fvpkmTJvz444988803jBw50mXAc2FYvHgx58+fz7a/Z8+eDB06lClTpjBw4EDWrFlD1apVmTNnDkuXLmXChAnOFqohQ4Zw/Phxrr/+eipXrkxiYiITJ06kadOmzvFC9evXp1OnTrRo0YKyZcvyxx9/MGfOHB5++OFCvR7xMNZMPhNxf5ebBt+gQYMcj1+6dKnRpk0bIzAw0KhYsaLxf//3f8aCBQsMwPj555+dx11uGnxOU465ZFr25abBDx8+PNtr4+LiXKZlG4ZhLF682GjWrJnh5+dn1KhRw3j//feNxx9/3AgICLjM38IFmzdvNjp37myEhIQY5cqVM+6//37ntOqLp3APGDDACA4Ozvb6nGo/duyY0a9fPyM0NNQICwsz+vXrZ6xduzbX0+CzfP/99wZgxMTEZJt67nA4jJdeesmIi4sz/P39jWbNmhnfffddts/BMK4+Dd4wDMNutxvjxo0zYmJijMDAQKNTp07Gpk2bsv19nz9/3nj88cedx7Vv395Yvny50bFjR6Njx44u7/vNN98Y9evXd96SIOvac6rx1KlTxmOPPWZUrFjR8PX1NWrVqmW8+uqrLtPys64lt98Xl8r6nrzc45NPPjEMwzAOHTpkDBo0yChXrpzh5+dnNGrUKNvnNmfOHOOmm24yoqKiDD8/P6NKlSrGAw88YBw4cMB5zIsvvmi0bt3aCA8PNwIDA426desa//73v420tLQr1ilyJTbDKEH/BRWREqdnz56agiwipY7GAImI06XLVmzfvp158+bRqVMnawoSESkiagESEaeYmBgGDhxI9erVSUxMZNKkSaSmprJ27dps97YREXFnGgQtIk5du3bl888/5+DBg/j7+9O2bVteeuklhR8RKXXUAiQiIiIeR2OARERExOMoAImIiIjH0RigHDgcDvbv30+ZMmXydBt6ERERsY5hGJw6dYqKFStmW4j3UgpAOdi/fz+xsbFWlyEiIiL5sGfPHipXrnzFYxSAcpB1i/Y9e/YQGhpqcTUiIiKSGykpKcTGxrosBnw5CkA5yOr2Cg0NVQASERFxM7kZvqJB0CIiIuJxFIBERETE4ygAiYiIiMfRGCARESn17HY76enpVpchBeTr64u3t3ehnEsBSERESi3DMDh48CDJyclWlyKFJDw8nAoVKhT4Pn0KQCIiUmplhZ+oqCiCgoJ0c1s3ZhgGZ8+e5fDhwwDExMQU6HwKQCIiUirZ7XZn+ImMjLS6HCkEgYGBABw+fJioqKgCdYdpELSIiJRKWWN+goKCLK5EClPW51nQMV0KQCIiUqqp26t0KazPUwFIREREPI4CkIiISClXtWpVJkyYYHUZJYoCkIiISAlhs9mu+Bg7dmy+zrt69WqGDh1aoNo6derEyJEjC3SOkkSzwIrbgfVQpiKElLe6EhERKWEOHDjg3J41axajR49m27Ztzn0hISHObcMwsNvt+Phc/Vd5+fL6nXMptQAVpwXPwJRrYcW7VlciIiIlUIUKFZyPsLAwbDab8/nWrVspU6YMP/zwAy1atMDf35/ff/+dv//+m1tvvZXo6GhCQkJo1aoVixYtcjnvpV1gNpuN999/n9tuu42goCBq1arFt99+W6Dav/zySxo0aIC/vz9Vq1blv//9r8vX3333XWrVqkVAQADR0dHceeedzq/NmTOHRo0aERgYSGRkJJ07d+bMmTMFqudqFICKU5W25p+rp0HqKWtrERHxQIZhcDYto9gfhmEU2jU89dRTvPzyy2zZsoXGjRtz+vRpunfvzuLFi1m7di1du3alR48eJCUlXfE848aN4+6772bDhg10796dvn37cvz48XzVtGbNGu6++2569+7Nxo0bGTt2LM899xzTp08H4I8//uCRRx7h+eefZ9u2bcyfP59rr70WMFu9+vTpw3333ceWLVtYsmQJt99+e6H+neVEXWDFqU53iKwJx3ZAwsfQdrjVFYmIeJRz6Xbqj15Q7O+7+fkuBPkVzq/c559/nhtvvNH5vGzZsjRp0sT5/IUXXuCrr77i22+/5eGHH77seQYOHEifPn0AeOmll3jrrbdYtWoVXbt2zXNNr7/+OjfccAPPPfccALVr12bz5s28+uqrDBw4kKSkJIKDg7nlllsoU6YMcXFxNGvWDDADUEZGBrfffjtxcXEANGrUKM815JVagIqTlxe0G2FuL38X7FqYT0RE8qZly5Yuz0+fPs0TTzxBvXr1CA8PJyQkhC1btly1Bahx48bO7eDgYEJDQ53LTOTVli1baN++vcu+9u3bs337dux2OzfeeCNxcXFUr16dfv368dlnn3H27FkAmjRpwg033ECjRo246667mDp1KidOnMhXHXmhFqDi1rg3/PRvSNkLm+ZCk15WVyQi4jECfb3Z/HwXS963sAQHB7s8f+KJJ1i4cCGvvfYaNWvWJDAwkDvvvJO0tLQrnsfX19fluc1mw+FwFFqdFytTpgwJCQksWbKEH3/8kdGjRzN27FhWr15NeHg4CxcuZNmyZfz4449MnDiRZ555hpUrV1KtWrUiqQfUAlT8fAMg/gFze9lbUMR9nCIicoHNZiPIz6fYH0V5N+qlS5cycOBAbrvtNho1akSFChXYvXt3kb1fTurVq8fSpUuz1VW7dm3nel0+Pj507tyZV155hQ0bNrB7925++uknwPxc2rdvz7hx41i7di1+fn589dVXRVqzWoCs0Gow/PY6HNoEf/8ENW+wuiIREXFTtWrVYu7cufTo0QObzcZzzz1XZC05R44cYd26dS77YmJiePzxx2nVqhUvvPACvXr1Yvny5bz99tu8+6456/m7775j586dXHvttURERDBv3jwcDgd16tRh5cqVLF68mJtuuomoqChWrlzJkSNHqFevXpFcQxa1AFkhMAKa9ze3l75pbS0iIuLWXn/9dSIiImjXrh09evSgS5cuNG/evEjea8aMGTRr1szlMXXqVJo3b84XX3zBzJkzadiwIaNHj+b5559n4MCBAISHhzN37lyuv/566tWrx+TJk/n8889p0KABoaGh/Prrr3Tv3p3atWvz7LPP8t///pdu3boVyTVksRlFPc/MDaWkpBAWFsbJkycJDQ0tmjdJToI3m4Jhh6G/QMWmRfM+IiIe6vz58+zatYtq1aoREBBgdTlSSK70uebl97dagKwSXgUa3m5uL5tobS0iIiIeRgHISu0eMf/88ys4kWhtLSIiIh5EAchKMY2h+nVmN5iWxxARESk2CkBWa5/ZCpTwMZzN3y3IRUREJG8sD0DvvPMOVatWJSAggPj4eFatWnXF4ydMmECdOnUIDAwkNjaWxx57jPPnzxfonJaqfh1UaATpZ801wkRERKTIWRqAZs2axahRoxgzZgwJCQk0adKELl26XPZW3DNmzOCpp55izJgxbNmyhWnTpjFr1iz+9a9/5fuclrPZoN2j5vaqKZB+ztp6REREPIClAej111/n/vvvZ9CgQdSvX5/JkycTFBTEBx98kOPxy5Yto3379txzzz1UrVqVm266iT59+ri08OT1nCVCg54QFgtnjsD6z62uRkREpNSzLAClpaWxZs0aOnfufKEYLy86d+7M8uXLc3xNu3btWLNmjTPw7Ny5k3nz5tG9e/d8n7NE8Pa9sDL8srfBYbe2HhERkVLOsqUwjh49it1uJzo62mV/dHQ0W7duzfE199xzD0ePHuWaa67BMAwyMjJ48MEHnV1g+TknQGpqKqmpqc7nKSkp+b2s/GvWD5a8DMf/hm3zoF6P4q9BRETEQ1g+CDovlixZwksvvcS7775LQkICc+fO5fvvv+eFF14o0HnHjx9PWFiY8xEbG1tIFeeBf4i5RhiYy2PoBt0iIpJPnTp1YuTIkVaXUaJZFoDKlSuHt7c3hw4dctl/6NAhKlSokONrnnvuOfr168eQIUNo1KgRt912Gy+99BLjx4/H4XDk65wATz/9NCdPnnQ+9uzZU/ALzI/WD4C3P+xdDUkrrKlBREQs06NHD7p27Zrj13777TdsNhsbNmwo8PtMnz6d8PDwAp/HnVkWgPz8/GjRogWLFy927nM4HCxevJi2bdvm+JqzZ8/i5eVasre3NwCGYeTrnAD+/v6Ehoa6PCxRJhqa9Da3l71lTQ0iImKZwYMHs3DhQvbu3Zvtax9++CEtW7akcePGFlRW+ljaBTZq1CimTp3KRx99xJYtWxg2bBhnzpxh0KBBAPTv35+nn37aeXyPHj2YNGkSM2fOZNeuXSxcuJDnnnuOHj16OIPQ1c5Z4rUbAdjMcUBHtlldjYiIFKNbbrmF8uXLM336dJf9p0+fZvbs2QwePJhjx47Rp08fKlWqRFBQEI0aNeLzzwt3BnFSUhK33norISEhhIaGcvfdd7v0rqxfv57rrruOMmXKEBoaSosWLfjjjz8ASExMpEePHkRERBAcHEyDBg2YN29eodZXGCwbBA3Qq1cvjhw5wujRozl48CBNmzZl/vz5zkHMSUlJLi0+zz77LDabjWeffZZ9+/ZRvnx5evTowb///e9cn7PEK1cL6t4MW78zF0m99W2rKxIRKT0Mw7zxbHHzDTLv+3YVPj4+9O/fn+nTp/PMM89gy3zN7Nmzsdvt9OnTh9OnT9OiRQuefPJJQkND+f777+nXrx81atSgdevWBS7V4XA4w88vv/xCRkYGw4cPp1evXixZsgSAvn370qxZMyZNmoS3tzfr1q3D19cXgOHDh5OWlsavv/5KcHAwmzdvJiQkpMB1FTabYWi07aVSUlIICwvj5MmT1nSHJa2ED24Cbz8YuRHKXH78koiI5Oz8+fPs2rWLatWqERAQYO5MOwMvVSz+Yv61H/yCc3Xo1q1bqVevHj///DOdOnUC4NprryUuLo5PPvkkx9fccsst1K1bl9deew0wB0E3bdqUCRMm5Hj89OnTGTlyJMnJydm+tnDhQrp168auXbuck4I2b95MgwYNWLVqFa1atSI0NJSJEycyYMCAbK9v3Lgxd9xxB2PGjMnV9eZVjp9rprz8/narWWAeo0o8xLYBexqsnGx1NSIiUozq1q1Lu3btnDfw3bFjB7/99huDB5szhe12Oy+88AKNGjWibNmyhISEsGDBApKSkgrl/bds2UJsbKzLjOj69esTHh7Oli1bAHO4yZAhQ+jcuTMvv/wyf//9t/PYRx55hBdffJH27dszZsyYQhm0XRQs7QKTK2j/CMxcAas/gA6Pg38ZqysSEXF/vkFma4wV75sHgwcPZsSIEbzzzjt8+OGH1KhRg44dOwLw6quv8uabbzJhwgQaNWpEcHAwI0eOJC0trSgqz9HYsWO55557+P777/nhhx8YM2YMM2fO5LbbbmPIkCF06dKF77//nh9//JHx48fz3//+lxEjRhRbfbmhFqCSqnY3iKwFqSdhzUdWVyMiUjrYbGZXVHE/cjH+52J33303Xl5ezJgxg48//pj77rvPOR5o6dKl3Hrrrdx77700adKE6tWr89dffxXaX1G9evXYs2ePyy1hNm/eTHJyMvXr13fuq127No899hg//vgjt99+Ox9++KHza7GxsTz44IPMnTuXxx9/nKlTpxZafYVFAaik8vLKnBEGrJgE9nRr6xERkWITEhJCr169ePrppzlw4AADBw50fq1WrVosXLiQZcuWsWXLFh544IFs97/LDbvdzrp161weW7ZsoXPnzjRq1Ii+ffuSkJDAqlWr6N+/Px07dqRly5acO3eOhx9+mCVLlpCYmMjSpUtZvXo19erVA2DkyJEsWLCAXbt2kZCQwM8//+z8WkmiAFSSNe4FwVGQshc2zbW6GhERKUaDBw/mxIkTdOnShYoVLwzcfvbZZ2nevDldunShU6dOVKhQgZ49e+b5/KdPn6ZZs2Yujx49emCz2fjmm2+IiIjg2muvpXPnzlSvXp1Zs2YB5v33jh07Rv/+/alduzZ333033bp1Y9y4cYAZrIYPH069evXo2rUrtWvX5t133y2Uv5PCpFlgObB8FtjFfn0NfnoBohrAsKV5bkYVEfFUV5otJO5Ls8A8RavB4BsMh/+Evxdf/XgRERG5KgWgki4wAlpk3mdh6ZvW1iIiIlJKKAC5gzYPgc0bdv0K+9dZXY2IiIjbUwByB+Gx0PAOc1uLpIqIiBSYApC7aP+I+eefX8GJ3ZaWIiLiTjTXp3QprM9TAchdVGgENa4HwwHLS950QhGRkiZrcc6zZy1Y/FSKTNbnmfX55peWwnAn7R6Bv3+CtZ9Ap6cgqKzVFYmIlFje3t6Eh4dz+PBhAIKCgpx3Uxb3YxgGZ8+e5fDhw4SHh+Pt7V2g8ykAuZPqncyWoIMbYfX70PH/rK5IRKREq1ChAoAzBIn7Cw8Pd36uBaEA5E5sNmg/Er4cDCunmEtl+AZaXZWISIlls9mIiYkhKiqK9HQtKeTufH19C9zyk0UByN3U7wmLxsHJJFj/ObS8z+qKRERKPG9v70L7xSmlgwZBuxtvH2j7kLm9bCI47NbWIyIi4oYUgNxRs34QEA7Hd8LW762uRkRExO0oALkj/xBoNcTcXvom6B4XIiIieaIA5K7iHwBvf9j3ByQtt7oaERERt6IA5K5CoqBpH3N7qZbHEBERyQsFIHfWdgRgg79+gMNbra5GRETEbSgAubNyNaHuzeb28onW1iIiIuJGFIDcXftHzT83fAEpB6ytRURExE0oALm72NYQ2wbsabBystXViIiIuAUFoNIgqxXojw/hfIq1tYiIiLgBBaDSoHZXKFcbUk9CwkdWVyMiIlLiKQCVBl5e5sKoACsmgV0L/omIiFyJAlBp0bgXhERDyj7Y9KXV1YiIiJRoCkClhY+/eXdoMG+MqOUxRERELksBqDRpeR/4hcDhP2HHYqurERERKbEUgEqTwAhoPsDcXvamtbWIiIiUYApApU2bYWDzhl2/wv61VlcjIiJSIikAlTbhsdDoTnNbi6SKiIjkSAGoNMqaEr/5azix28pKRERESiQFoNKoQiOocQMYDlj+jtXViIiIlDgKQKVV+0fMPxM+gTPHrK1FRESkhFEAKq2qdYQKjSHjHKx+3+pqREREShQFoNLKZruwSOqq9yD9nLX1iIiIlCAKQKVZ/Z4QXgXOHoV1M6yuRkREpMRQACrNvH2gzXBze9lEcNitrUdERKSEUAAq7Zr3M+8QfWIXbP3O6mpERERKBAWg0s4vGFoNMbeXvqlFUkVERFAA8gyth4K3P+xbA4nLrK5GRETEcgpAniAkCpreY24v0/IYIiIiCkCeot0IwAZ/zYfDW62uRkRExFIKQJ4isgbUu8XcXjbR2lpEREQspgDkSdpl3hhxwyxIOWBtLSIiIhZSAPIksa2gSltwpMPKSVZXIyIiYhkFIE+TtTzGHx/C+RRraxEREbGIApCnqdUFytWG1BRI+MjqakRERCyhAORpvLwyZ4QBy9+FjDRr6xEREbGAApAnatwLQqLh1H7Y9KXV1YiIiBQ7BSBP5OMP8Q+a28ve0vIYIiLicRSAPFXL+8AvBA5vhh2LrK5GRESkWCkAearAcGgx0Nxe+qaVlYiIiBQ7BSBP1mYYePnA7t9gX4LV1YiIiBQbBSBPFlYZGt5pbmuRVBER8SAKQJ4ua0r85m/g+C5raxERESkmCkCerkJDqHEDGA5Y/o7V1YiIiBQLBSC5sDzG2k/hzDFraxERESkGCkAC1a6FmCaQcQ5Wv291NSIiIkVOAUjAZoN2j5jbq6ZA2llr6xERESliCkBiqt8TwqvA2WOwfobV1YiIiBQpBSAxeftA24fN7WVvg8NubT0iIiJFSAFILmh2LwRGwIldsOV/VlcjIiJSZBSA5AK/YGh1v7m99E0tkioiIqVWiQhA77zzDlWrViUgIID4+HhWrVp12WM7deqEzWbL9rj55pudxwwcODDb17t27Vocl+L+Wg8FnwDYnwCJS62uRkREpEhYHoBmzZrFqFGjGDNmDAkJCTRp0oQuXbpw+PDhHI+fO3cuBw4ccD42bdqEt7c3d911l8txXbt2dTnu888/L47LcX8h5aHpPeb2Ui2PISIipZPlAej111/n/vvvZ9CgQdSvX5/JkycTFBTEBx98kOPxZcuWpUKFCs7HwoULCQoKyhaA/P39XY6LiIgojsspHdo+DNhg+wI4vMXqakRERAqdpQEoLS2NNWvW0LlzZ+c+Ly8vOnfuzPLly3N1jmnTptG7d2+Cg4Nd9i9ZsoSoqCjq1KnDsGHDOHbs8nc4Tk1NJSUlxeXh0SJrQL1bzO1lE62tRUREpAhYGoCOHj2K3W4nOjraZX90dDQHDx686utXrVrFpk2bGDJkiMv+rl278vHHH7N48WL+85//8Msvv9CtWzfs9pyndo8fP56wsDDnIzY2Nv8XVVq0H2n+ueELSNlvaSkiIiKFzfIusIKYNm0ajRo1onXr1i77e/fuzT/+8Q8aNWpEz549+e6771i9ejVLlizJ8TxPP/00J0+edD727NlTDNWXcJVbQpV24EiHlZOtrkZERKRQWRqAypUrh7e3N4cOHXLZf+jQISpUqHDF1545c4aZM2cyePDgq75P9erVKVeuHDt27Mjx6/7+/oSGhro8hAuLpP7xIZz38G5BEREpVSwNQH5+frRo0YLFixc79zkcDhYvXkzbtm2v+NrZs2eTmprKvffee9X32bt3L8eOHSMmJqbANXuUWjdBuTqQmgJrpltdjYiISKGxvAts1KhRTJ06lY8++ogtW7YwbNgwzpw5w6BBgwDo378/Tz/9dLbXTZs2jZ49exIZGemy//Tp0/zzn/9kxYoV7N69m8WLF3PrrbdSs2ZNunTpUizXVGp4eUG7Eeb2ikmQkWZtPSIiIoXEx+oCevXqxZEjRxg9ejQHDx6kadOmzJ8/3zkwOikpCS8v15y2bds2fv/9d3788cds5/P29mbDhg189NFHJCcnU7FiRW666SZeeOEF/P39i+WaSpXGd8NPL8Kp/bBpzoV7BImIiLgxm2FovYNLpaSkEBYWxsmTJzUeCOD3N2DRWChfDx5aDjab1RWJiIhkk5ff35Z3gYkbaDEI/ELgyBbYvtDqakRERApMAUiuLjAcWgw0t5dpeQwREXF/CkCSO22GgZcP7P4N9q2xuhoREZECUQCS3AmrDA3vNLe1SKqIiLg5BSDJvfaPmH9u+RaO77S2FhERkQJQAJLci24ANTuD4YDl71hdjYiISL4pAEneZC2PsfYzOHPM2lpERETySQFI8qZqB4hpChnnYPVUq6sRERHJFwUgyRub7cJYoJVTIO2stfWIiIjkgwKQ5F29WyE8Ds4dh3WfWV2NiIhInikASd55+0Dbh83t5W+Dw25tPSIiInmkACT506wvBEbAid3mtHgRERE3ogAk+eMXDK2HmttL3wKtqSsiIm5EAUjyr/VQ8AmA/QmQuNTqakRERHJNAUjyL7gcNO1rbi9909paRERE8kABSAqm7XDABtt/hEObra5GREQkVxSApGAia0C9Hub2sonW1iIiIpJLCkBScFnLY2ycDSn7ra1FREQkFxSApOAqt4S49uBIhxWTrK5GRETkqhSApHC0y1we448P4fxJa2sRERG5CgUgKRy1boLydSHtFKyZbnU1IiIiV6QAJIXDywvajTC3V0yCjDRr6xEREbkCBSApPI3ugjIxcOqAOSBaRESkhFIAksLj4w/xD5rbyyZqeQwRESmxFICkcLUcBH5l4MgW2L7Q6mpERERypAAkhSsgDFoONLe1PIaIiJRQCkBS+OKHgZcPJP4Oe9dYXY2IiEg2CkBS+MIqmQOiAZapFUhEREoeBSApGllT4rf8D47vtLYWERGRSygASdGIbgA1bwTDAcvfsboaERERFwpAUnTaZy6PsfZTOHPU2lpEREQuogAkRadqB6jYDDLOw6qpVlcjIiLipAAkRcdmu7BI6qr3IO2stfWIiIhkUgCSolXvHxBRFc4dh3WfWV2NiIgIoAAkRc3bB9o+bG4vfxvsGdbWIyIiggKQFIemfSGwLJzYDVu+tboaERERBSApBn5B0Hqoub3sLS2SKiIillMAkuLR+n7wCYD9a2H371ZXIyIiHk4BSIpHcDmzKwy0SKqIiFhOAUiKT9vhYPOCHQvh0GarqxEREQ+mACTFJ7IG1Othbi+baG0tIiLi0RSApHi1f9T8c+MXcHKftbWIiIjHUgCS4lWpBcRdA44MWDnJ6mpERMRDKQBJ8ctaJPWP6XD+pKWliIiIZ1IAkuJX80YoXw/STsEfH1pdjYiIeCAFICl+Xl7QboS5vXIyZKRaW4+IiHgcBSCxRqO7oEwMnDoAG2dbXY2IiHgYBSCxho8ftBlmbi+bCA6HtfWIiIhHUQAS67QYCH5l4MhW8+aIIiIixUQBSKwTEAYtB5rbWh5DRESKkQKQWCt+GHj5QuJS2LvG6mpERMRDKACJtcIqmQOiAZapFUhERIqHApBYL2tK/OZv4djf1tYiIiIeQQFIrBddH2rdBBiw/B2rqxEREQ+gACQlQ7vM5THWfQanj1hbi4iIlHr5CkB79uxh7969zuerVq1i5MiRvPfee4VWmHiYqtdAxeaQcR5WT7W6GhERKeXyFYDuuecefv75ZwAOHjzIjTfeyKpVq3jmmWd4/vnnC7VA8RA224VFUldNhbQz1tYjIiKlWr4C0KZNm2jdujUAX3zxBQ0bNmTZsmV89tlnTJ8+vTDrE09S7x8QURXOHYe1n1ldjYiIlGL5CkDp6en4+/sDsGjRIv7xj38AULduXQ4cOFB41Yln8fKGtg+b28vfBnuGtfWIiEipla8A1KBBAyZPnsxvv/3GwoUL6dq1KwD79+8nMjKyUAsUD9O0LwRFQnIibPnW6mpERKSUylcA+s9//sOUKVPo1KkTffr0oUmTJgB8++23zq4xkXzxC4LWQ83tpW+CYVhbj4iIlEo2w8jfbxi73U5KSgoRERHOfbt37yYoKIioqKhCK9AKKSkphIWFcfLkSUJDQ60ux/OcOQZvNICMczDgf1DtWqsrEpGSJCMNfPysrkJKoLz8/s5XC9C5c+dITU11hp/ExEQmTJjAtm3b3D78SAkQHAnN+prbS9+ythYRKTkMA/74AP5TFabfAqcPW12RuLF8BaBbb72Vjz/+GIDk5GTi4+P573//S8+ePZk0aVKhFigequ1wsHnBjoVw6E+rqxERq6Wegi+HwHePQfoZ2P0bTOkI+7SIsuRPvgJQQkICHTp0AGDOnDlER0eTmJjIxx9/zFtv6X/sUgjKVjenxQMsm2htLSJirYOb4L1OsGkO2Lzh2v+DcrXh1H74oJtumyH5kq8AdPbsWcqUKQPAjz/+yO23346Xlxdt2rQhMTGxUAsUD5Z1Y8SNs+Hk3isfKyKlj2HAmunw/g1wbAeEVoZBP8D1z8CQxVDnZrCnwjcPwbx/gj3d6orFjeQrANWsWZOvv/6aPXv2sGDBAm666SYADh8+nK9Bw++88w5Vq1YlICCA+Ph4Vq1addljO3XqhM1my/a4+eabnccYhsHo0aOJiYkhMDCQzp07s3379rxfqFirUguo2gEcGbBCXasiHiX1NMwdCv971Fwip9ZN8OBvUCXe/HpAKPT6FDr9y3y+6j34+FatJSi5lq8ANHr0aJ544gmqVq1K69atadu2LWC2BjVr1ixP55o1axajRo1izJgxJCQk0KRJE7p06cLhwzkPbps7dy4HDhxwPjZt2oS3tzd33XWX85hXXnmFt956i8mTJ7Ny5UqCg4Pp0qUL58+fz8/lipWyFkld8xGcS7a0FBEpJof+NLu8Nn5hdnl1Hgd9ZkFQWdfjvLyg05PQZyb4h0LiUnivI+xLsKRscS/5ngZ/8OBBDhw4QJMmTfDyMnPUqlWrCA0NpW7durk+T3x8PK1ateLtt98GwOFwEBsby4gRI3jqqaeu+voJEyYwevRoDhw4QHBwMIZhULFiRR5//HGeeOIJAE6ePEl0dDTTp0+nd+/eVz2npsGXIIYB77aFI1ug81i45jGrKxKRomIYsPYTszsr4zyUqQh3fQhV2lz9tUe3w+d94Nh28PaHHhOg6T1FXrKULEU+DR6gQoUKNGvWjP379ztXhm/dunWewk9aWhpr1qyhc+fOFwry8qJz584sX748V+eYNm0avXv3Jjg4GIBdu3Zx8OBBl3OGhYURHx9/2XOmpqaSkpLi8pAS4uJFUldMhoxUa+sRkaKRdga+ehC+HWGGn5qd4cHfcxd+AMrVgvsXQ53u5rigr4fBvP/TuCC5rHwFIIfDwfPPP09YWBhxcXHExcURHh7OCy+8gMPhyPV5jh49it1uJzo62mV/dHQ0Bw8evOrrV61axaZNmxgyZIhzX9br8nLO8ePHExYW5nzExsbm+hqkGDS80/yf4OmD5oBoESldDm2G966DDTPNLq8bxsA9s817guVFQBj0+gw6PW0+XzUFPu6pcUGSo3wFoGeeeYa3336bl19+mbVr17J27VpeeuklJk6cyHPPPVfYNV7WtGnTaNSoUYGX33j66ac5efKk87Fnz55CqlAKhY8ftBlmbi99C/IQskWkhFv7GUy9Ho5ugzIxMPA76DDKHN+TH15e0Okp6P05+JWBxN/N8UQaFySXyNd32EcffcT777/PsGHDaNy4MY0bN+ahhx5i6tSpTJ8+PdfnKVeuHN7e3hw6dMhl/6FDh6hQocIVX3vmzBlmzpzJ4MGDXfZnvS4v5/T39yc0NNTlISVMi4HmIMej22D7j1ZXIyIFlXYGvhpmTmHPOAc1bjC7vOLaFc7563aH+3+CyFqQshc+6ArrPi+cc0upkK8AdPz48RzH+tStW5fjx4/n+jx+fn60aNGCxYsXO/c5HA4WL17snFl2ObNnzyY1NZV7773XZX+1atWoUKGCyzlTUlJYuXLlVc8pJVhAqBmCwFwkVUTc1+GtZqvP+hnmHd+vfxb6zoHgcoX7PuVrm+OCanfLHBf0IPzwpMYFCZDPANSkSRPnrK2Lvf322zRu3DhP5xo1ahRTp07lo48+YsuWLQwbNowzZ84waNAgAPr378/TTz+d7XXTpk2jZ8+eREa69hHbbDZGjhzJiy++yLfffsvGjRvp378/FStWpGfPnnmqTUqYNsPAyxeSlsHeP6yuRkTyY93nMPU6OLIVQiqYCx5f+8/8d3ldTUAY9J4BHTNnFa+crHFBAoBPfl70yiuvcPPNN7No0SJnq8ry5cvZs2cP8+bNy9O5evXqxZEjRxg9ejQHDx6kadOmzJ8/3zmIOSkpyTnNPsu2bdv4/fff+fHHnLtC/u///o8zZ84wdOhQkpOTueaaa5g/fz4BAQH5uFopMUIrQuO7Yd1nZitQr0+srkhEcivtLPzwT1j7qfm8+nVw+1QIKV/07+3lBdc9DRUamTPNssYF9f4UKubt3nVSeuT7PkD79+/nnXfeYevWrQDUq1ePoUOH8uKLL/Lee+8VapHFTfcBKsEOb4F32wA2GLEGImtYXZGIXM2Rv2D2ADi82ezy6vQ0dHgcvLwtqGUbzLzHXFrDJwB6vAlNrn5/OHEPefn9ne8AlJP169fTvHlz7HZ7YZ3SEgpAJdxnd8P2BdDyPrjlDaurEZErWT/rwgruIdFwx/tQ7Vprazp/0lxm46/55vP4YXDTC+Dta21dUmDFciNEEctk3Rhx3Qz144uUVOnnzJsafjXUDD/VOpqzvKwOP5A5Luhzc1V5gJWT4JPb4MxRa+uSYqUAJO4nrr25UGrGeXMBRBEpWY5uh6k3QMLHgM3s8ur3FYREWV3ZBV5e5qryvT4DvxDY/Zs5Lmj/Oqsrk2KiACTux2a7sEjq6qnm/UREpGTYMBumdITDf0JwFPT/2rwxoRXjfXKj3i0wZDGUrQEn98AHXcxuOyn18jQL7Pbbb7/i15OTkwtSi0ju1esBEdXgxC7zTrLxQ62uSMSzpZ+D+U/Bmunm86od4I5pUCb6ii8rEaLqmjdNnDvUHF/41VA4sA5ufAG88zVZWtxAnlqALl4vK6dHXFwc/fv3L6paRS7w8oZ2D5vbyyeCPcPaekQ82dEd8P6NmeHHBh2fhP7fuEf4yRIYDn1mXhgXtOJd+KSnxgWVYoU6C6y00CwwN5F+Dt5oAGePwZ0fQMM7rK5IxPNs+hK+fQTSTkNQObhjKtS43uqqCmbL/8z7BaWdhrBY6PUpVGxqdVWSC5oFJp7BNxBaZ3Z9LX0TlOVFik/6eXN6+5z7zKAQd405y8vdww+YXeyXjgva8IXVVUkhUwAS99bqfvAJhAPrYdevVlcj4hmO/Q3TOsMfHwA26PCE2eUVGmN1ZYUna1xQrZvMGadz74f5/1J3eymiACTuLTgSmmUuiLvsLWtrEfEEf35lzvI6uBGCIuHeOXDDc6VzsHBgOPSZZa5VBrDiHfj0NjhzzNKypHAoAIn7azvcvL3+jkVwcJPV1YiUThmp8P0TMHsgpJ2CKu3MLq+ana2urGh5Za5Wf/cn4BtstjS/18lsdRa3pgAk7q9sNah/q7m9bKK1tYiURsd3wrQbzftuAVwzylzFPbSitXUVp/r/gPsXQ9nqcDIJpnUx73kkbksBSEqHrBsjbpoDJ/daW4tIabL5G7PL68B6CCwLfedA5zGls8vraqLqwf0/Q80bIeMczB0CC57RuCA3pQAkpUOl5uaN1xwZsGKS1dWIuL+MVJj3f/BFf0hNgdg2ZpdXrRutrsxageFwzyxzNXuA5W/Dp7drXJAbUgCS0qP9o+afa6bDuWQrKxFxb8d3mVO/V00xn7cfCQO/g7BKlpZVYnh5ww2j4e6PM8cF/QJTO8GBDVZXJnmgACSlR83OEFXfvCfJHx9YXY2Ie9ryP7PLa/9aCIyAe76AG8eBt6/VlZU89W+FIYvMZXmSk2DaTbBxjtVVSS4pAEnpcfEiqSsnm034IpI7GWnww1Mw615IPQmVW5tdXrW7WF1ZyRZdH4b+bP4HLOMcfDlY44LchAKQlC4N74AyFeH0Id25VSS3TiSaXV4rM8fPtXsEBs2DsMrW1uUuslrKrhllPl/+Nnx2B5w9bm1dckUKQFK6+PhBm2Hm9rK3wOGwth6Rkm7r9zClA+xPgIBwc0HQm15Ql1deeXmbs+Pu+sgcF7RzCbyXecNIKZEUgKT0aTEQ/EPh6F+wfYHV1YiUTBlp5tIOM++B8yehUkt48Deo083qytxbg56u44Lev1HjgkooBSApfQJCoeUgc3uplscQySY5CT7sZi7tAND2YRj0A4RXsbau0iJrXFCNGy6MC/rxOY0LKmEUgKR0ih8GXr6QtAz2rLa6GpGSY9sPMLkD7PsDAsKg9wzo8m+z+1gKT2AE9J0N1zxmPl/2lsYFlTAKQFI6hcZA417m9rI3ra1FpCSwp8OPz8LnveF8MlRqAQ/8BnVvtrqy0svLGzqPhbumg29Q5rigThoXVEIoAEnp1W6E+eeW7+DY39bWImKl5D1ml1fWWnltHoJB8yEiztq6PEWD2zLHBVWF5ETzfkGbvrS6Ko+nACSlV1RdqN0VMLRIqniuvxaYs7z2rgb/MOj1KXQdry6v4hbdwFxHrMb1kH4W5txnjgty2K2uzGMpAEnplnVjxHUz4PRha2sRKU72dFg4GmbcDedOQMVm8OCvUK+H1ZV5rqDMxWTbjzSfL3sLPtW4IKsoAEnpFtfOHOtgT4VV71ldjUjxOLkXpt8MSzPHv8U/CPctMLtgxFpe3ubSInd+mDku6GeYeh0c3GR1ZR5HAUhKN5vtwiKpq9+HtDPW1iNS1LYvNGd57Vlp3g/r7o+h23/Ax9/qyuRiDW+HwQshPA5O7IZpN8KmuVZX5VEUgKT0q3sLlK1udgOs/dTqakSKhj0DFo2Fz+6Ec8chpgk88Iu5YKeUTBUawtAlF40LGgQLx2hcUDFRAJLSz8vbvNEbmGv06GZkUtqk7IePboHf3zCftx5qti6UrW5tXXJ1znFBmS3VSyfAZ3dpXFAxUAASz9D0HggqZ94Bd/PXVlcjUnh2LILJ10DScvArY95zpvur6vJyJ17ecOPzcOcH5rigvxeb44IO/Wl1ZaWaApB4Bt9A83/FYM68MAxr6xEpKHsGLH4+cxbRMajQyOzyanCb1ZVJfjW8Awb/eGFc0Pud4c+vrK6q1FIAEs/R+n7zf1cH1sOuX6yuRiT/Ug7Ax/+A3/5rPm85GAYvgsga1tYlBVehkTkuqPp15rig2QPNsV0aF1ToFIDEcwSVhWb3mttaJFXc1d8/mV1eiUvNLq87P4BbXgffAKsrk8KSNS4o6z5mv7+hcUFFQAFIPEvb4WDzMvvYdd8NcScOO/z0b/jkdjh7FKIzu7wa3mF1ZVIUvH3gphfgjmngE6hxQUVAAUg8S0RVqN/T3F6mViBxE6cOwse3wq+vAAa0GARDFqrLyxM0utP8rMOrZI4LuhH+/NrqqkoFBSDxPO0zm5U3fWkuEilSku1cYnZ57f4N/ELMFoEeE8yB/eIZKjSCob9A9U6QfgZmD4BF4zQuqIAUgMTzVGwGVTuAIwNWTLK6GpGcOezw83j4uCecOQJRDczBsY3utLoysUJQWej7JbQbYT7//fUL67xJvigAiWfKWoww4SM4l2xlJSLZnToEn/SEX14GDGg+AO5fDOVqWV2ZWMnbB2568cK4oB2L4L3r4NBmqytzSwpA4plq3mD+jzrtNPzxgdXViFyw8xezy2vXr+AbDLdPhX+8pS4vuaDRnZn3C6oCJ3aZ9wva/I3VVbkdBSDxTDbbhbFAKydDRqq19Yg47LDkP2bLz5nDEFXf7PJqfLfVlUlJFNMY7l8C1Tqa44K+6G/eGFPjgnJNAUg8V8M7ILQSnD4EG2ZZXY14stOH4dPbYclLYDigWT8YshjK17a6MinJgiPh3rkX1jr87b8wo5e69XNJAUg8l7cvtBlmbi+bCA6HtfWIZ9r1m9nltXOJeafynpPh1rfBL8jqysQdePtAl3/D7e9njgtaaN4v6PAWqysr8RSAxLM1HwD+YXD0L/hrvtXViCdxOOCXV80lLU4fgvJ14f6foWkfqysTd9T4Lhi8AMKqwPGdMPUG2Pyt1VWVaApA4tkCQqHlIHNbN0aU4nL6iNnl9fOLZpdX075w/08QVdfqysSdxTQxx41VuzZzXFA/WPyCxgVdhgKQSPyD4OULScthzyqrq5HSbvdSmNIBdv5sdlnc+i70fBf8gq2uTEqD4Ei496uLxgW9Bp/31rigHCgAiYTGQJNe5vbSN62tRUovh8McpPrRLXDqAJSrA0N/hmZ9ra5MShvnuKCp4BMA23+EqdfD4a1WV1aiKACJwIVVl7d+D0d3WFuLlD5njsJnd5rTlA0HNO6d2eVVz+rKpDRrfLd5v6CwKnD8b3j/BtjyP6urKjEUgEQAyteB2t0AA5ZPtLoaKU0Sl8PkDuZq3j4B8I+34bbJ4B9idWXiCbLGBVXtYN74dda98NOLmvWKApDIBVk3Rlz3uXlfFpGCcDjg9zdg+s1waj9E1jJbfZr3M2/EKVJcgiOh39fQZrj5/NdXNS4IBSCRC6q0hUotwZ4Kq96zuhpxZ2eOwee9YNFYMOzQ6G7zf+HRDayuTDyVtw90fQluey9zXNACjx8XpAAkksVmg/aPmturpkLqaWvrEfeUtMKc5bX9R/MXTY+34Pb31OUlJUOTXnDfAgiLvWhc0HdWV2UJBSCRi9W9GcpWh/PJsPZTq6sRd+JwmLMIP+wOKfsgsqa5nEWLAerykpKlYtNLxgX1hZ/+7XHjghSARC7m5X3h/hnL3wF7hrX1iHs4e9wcU7FwtNnl1fAO8xdMhYZWVyaSs+BymeOCHjKf//oKzOwD509aWlZxUgASuVTTeyCoHJxMgs1fW12NlHR7VpuzvLYvAG9/uGUC3DEN/MtYXZnIlXn7QNfxcNsUs7v2r/nmuKAj26yurFgoAIlcyjcQ4h8wt5e+CYZhbT1SMhmGuYjuh10hZa/ZdTpkkbm0irq8xJ006Q33zYfQynBsh7mO2Nbvra6qyCkAieSk1RBzZe6DG8xVukUudvY4fN4HfnwWHBnQ4DYY+gvENLa6MpH8qdgMHvglc1zQKZh5D/z8UqkeF6QAJJKToLLQrJ+5rUVS5WJ7/4ApHeGvH8DbD27+L9z5obmwrog7Cy4H/b6C+GHm81/+YwahUjouSAFI5HLaPgQ2L/j7Jzi40epqxGqGAcvfhQ+6muPDIqqZXV6thqjLS0oPb1/o9jL0nGyOafvrB7NL7MhfVldW6BSARC4noqrZtQGwVK1AHu3cCZjZFxY8DY50qH+r2V0Q08TqykSKRtM+MHhB5rig7ebg6K3zrK6qUCkAiVxJ1iKpm76E5D3W1iLW2LcGplwL2743u7y6vwZ3fQQBYVZXJlK0KjYzb+cQd03muKA+8PP4UjMuSAFI5EoqNoVq15r3dlkxyepqpDgZBqyYDNO6QHIShMeZK2u3vl9dXuI5QspD/6+hdebM2F9eNm+ceD7F0rIKgwKQyNVkLY+xZrrZFSKl37lk+KIfzH/S7PKq1wMe+NX8H7GIp/H2he6vQM9J5rigbfMy7xfk3uOCFIBErqbGDRDdENLPwB8fWF2NFLV9CWaX15b/gZcvdHsF7v4EAsOtrkzEWk3vybxfUKVSMS5IAUjkamw2aDfC3F4xGdLPW1uPFA3DgJXvwQddIDkRwquYg0DjH1CXl0iWSs0zxwW1vzAuaMnLbjkuSAFIJDca3mHOhjhzGDbMsroaKWznT8LsAfDDP8GeBnVvMbu8KrWwujKRkickCvp/c2Fc0JLxMOtetxsXZHkAeuedd6hatSoBAQHEx8ezatWqKx6fnJzM8OHDiYmJwd/fn9q1azNv3oUmuLFjx2Kz2VwedevWLerLkNLO2xfaZN4cbNlE2LsGDmww18w5vgtO7oMzR81/ADJStXyGO9m/zuzy2vwNePlAl/HQ61MIjLC6MpGSK2tc0K3vZo4L+h7evwGObre6slzzsfLNZ82axahRo5g8eTLx8fFMmDCBLl26sG3bNqKiorIdn5aWxo033khUVBRz5syhUqVKJCYmEh4e7nJcgwYNWLRokfO5j4+llymlRYsB8MsrZt/3+9df/XgvX/DxN/+h8PYHHz/zT2+/i7azjrl42+/Cn87trHNcut/vkvNdtO2T+bVLt70s/39PyWAYsPp9WPAvs9UnrArc9SFUbml1ZSLuo1lfiKoLM++Fo3+Z44Jufw/qdLO6squyNBm8/vrr3H///QwaNAiAyZMn8/333/PBBx/w1FNPZTv+gw8+4Pjx4yxbtgxfX18Aqlatmu04Hx8fKlSoUKS1iwfyLwNdXzIXSE0/D/ZUs7XHnm5uOzJcj3ekQ1q6NbVeiZfPJSHqkvB1cbjKFuAuF67yEuYuE+y8vIvv7+B8CvzvEfjzK/N5ne5w6zvmEigikjeVWpg3Bv1iACQtg897Q6d/wbX/LNH/4bIsAKWlpbFmzRqefvpp5z4vLy86d+7M8uXLc3zNt99+S9u2bRk+fDjffPMN5cuX55577uHJJ5/E2/vCP57bt2+nYsWKBAQE0LZtW8aPH0+VKlUuW0tqaiqpqanO5ykp7tWPKcWo2b3mIycOh9mSYE+FjLRLtjODUkbqJdtpF/7McTv9oqCVtT/rfBdtZ3u/i87huCSEOTLMR/rZov/7ygub9+Vbsy4OV87wdbVwdZnWMcMBP70Ix3eaYbDzOGg7XAOdRQoiJAoGfGu2qK56D5a8ZC4m3XNSiV0nz7IAdPToUex2O9HR0S77o6Oj2bp1a46v2blzJz/99BN9+/Zl3rx57Nixg4ceeoj09HTGjBkDQHx8PNOnT6dOnTocOHCAcePG0aFDBzZt2kSZMmVyPO/48eMZN25c4V5gDr5Zt49PlicS6OdNkJ83QX4+BPhmbXub+33NPwP9fAjyvWi/nw+Bvt7O1wb6euPlpX+wSxQvL/AKAN8AqytxZRhXCVpp2VuzXLYLEuauEuBc6rRDxjnzURxCK5tdXrGti+f9REo7b1/o/qq5RMx3j8HW7+D9v6D351CuptXVZeNWg2McDgdRUVG89957eHt706JFC/bt28err77qDEDdul3od2zcuDHx8fHExcXxxRdfMHjw4BzP+/TTTzNq1Cjn85SUFGJjYwu9/j3Hz/JHYuHdSC/A14tA38xwdFEwygpNgb4+ruEq8+uBfj7ZAlfQRaEr0M8bfx8vbPofcelgs5ktIj7+VlfiyjAuBKSLA9WVWrMuDVyXC3ZXDHOpEFUPbnxBXV4iRaHZvVC+njkz7OhfMPU6uH0q1OlqdWUuLAtA5cqVw9vbm0OHDrnsP3To0GXH78TExODr6+vS3VWvXj0OHjxIWloafn5+2V4THh5O7dq12bFjx2Vr8ff3x9+/6H85dG8UQ43yIZxNs3M23c75NHvmdgbnMrfPpdszty/sO5tm53z6ha9nOZ/u4Hy6gxNnC3+ciZcNl7B0IVRlD0sXgpUZxrIfm32/r3fJ7ReWYmKzmV1SPtl/bkXEzVXOGhfUH5KWm+OCrvsXdHiixIwLsiwA+fn50aJFCxYvXkzPnj0Bs4Vn8eLFPPzwwzm+pn379syYMQOHw4FX5l/gX3/9RUxMTI7hB+D06dP8/fff9OvXr0iuIy+qlw+hevmQAp3D4TA4n5EZhjID0dlLAtOFEGXnXFpGtn1n03Pefy7NTprdvJmVw4AzaXbOpNmvUlH++HrbnK1XFweji7sHs4Uo38vszzxHVneiugdFREqAkCjo/y0seNqccfnzv+HAerhtsjmpxGI2w7DuhiWzZs1iwIABTJkyhdatWzNhwgS++OILtm7dSnR0NP3796dSpUqMHz8egD179tCgQQMGDBjAiBEj2L59O/fddx+PPPIIzzzzDABPPPEEPXr0IC4ujv379zNmzBjWrVvH5s2bKV++fK7qSklJISwsjJMnTxIaWjIHbxWVDLvDNSyl2TmXnpFD4LpMuMo8/uIwlvXas2kZOIrpuy3A1yvHcVOXHXuV2ap12cB1UXehugdFRPIo4RP4fpTZRV2uDvSeUSTjgvLy+9vSMUC9evXiyJEjjB49moMHD9K0aVPmz5/vHBidlJTkbOkBiI2NZcGCBTz22GM0btyYSpUq8eijj/Lkk086j9m7dy99+vTh2LFjlC9fnmuuuYYVK1bkOvx4Oh9vL8p4e1EmwLfQz20YBml2h2tYcoakjGwtVOfSHK7dg1ktXdkC14XzZDG7B9OuUE3+Bfp606hyGC3iImhRJYLmcRGUDVY3jojIZTXvZ469m3UvHN1m3i/ojqlQu4tlJVnaAlRSeXILkDu7tHvwbA7h6pxLq1TGRYEr8zUXdw9e0oKV1T2Yk2rlgmleJcIMRXER1IoKUTeciMilTh0yxwXtWQHNB8A/3irU0+fl97cCUA4UgCQnGXYHZ9PtHDp5noSkE6xJPEFCUjI7Dp/OdmwZfx+aVgl3BqKmseFF0qomIuJ2MtJg5SSIf7DQZ6cqABWQApDkRfLZNNYmJWcGohOs25PM2UsGj9tsUCe6DM0v6jarGhmksUQiIoVIAaiAFICkIDLsDrYePEVC0gkSEk+wJukEe45nv7lf2WA/Z7dZ8yrhNK4cTqBfMS4HISJSyigAFZACkBS2wylmt1lCZkvRxr0ns40p8vGy0aBiKM0uGktUMTzQoopFRNyPAlABKQBJUUvNsPPn/hSzhSjzcfhUarbjYsICaJ7ZZdYiLoL6MaH4+ZSMm4iJiJQ0CkAFpAAkxc0wDPYlnzPHEWV2m205cAr7JTdO8vfxonHlMJexROVCStgSFyIiFlEAKiAFICkJzqZlsH7PyYtmnJ0gOYdlT+Iig5xhqEVcBLWjy+CtKfgi4oEUgApIAUhKIsMw2Hn0jLOVKCHpBH8dyj4FP8Tfh6ax4c5A1DQ2nLBATcEXkdJPAaiAFIDEXZw8m87aPVmBKJm1SSeyrd9ms0GtqJDM2WZmS1H1csGagi8ipY4CUAEpAIm7sjsMth08xZqkE6zNHEuUeOxstuMignydYah5lQiaxIYR5GfpyjgiIgWmAFRACkBSmhw5lcraJDMMJSSeYP3ek6RluE7B9/ayUT8mlBZxETTLvIN1pfBAtRKJiFtRACogBSApzdIyHGw+kHJhxlniCQ6mnM92XHSov0u3WYOKofj76EaNIlJyKQAVkAKQeJr9mVPws2abbd6fQsYlU/D9fLxoXCnM2W3WPC6cqDIBFlUsIpKdAlABKQCJpzuXZmfD3mRnt1lCUjLHz6RlO65K2SCaZ3aZNY+LoE50GXy8daNGEbGGAlABKQCJuDIMg93HzjpbidYmnWDboVNc+q9HsJ83TWIvBKLmsRGEBWkKvogUDwWgAlIAErm6lPPprMtc2ywh6QTrkpI5lZqR7biaUSG0yFr0NS6c6uVC8NKNGkWkCCgAFZACkEje2R0G2w+fIiHxQijadfRMtuPCAn0vdJtViaBJbDjB/pqCLyIFpwBUQApAIoXj2OlU1iaZY4nWJJ5g/Z5kUi+Zgu9lg3qZU/CbZ7YUVY7QFHwRyTsFoAJSABIpGmkZDrYcSLmwvlniCfafzD4Fv3wZ/8z1zcyWogYVwwjw1RR8EbkyBaACUgASKT4HTp5zdputSTrBn/tOZp+C7+1Fw0qhzhaiFnERRIVqCr6IuFIAKiAFIBHrnE+3s3HfyQv3JUo8wbEcpuBXjgh0CUR1K2gKvoinUwAqIAUgkZLDMAySjp+96EaNyWw7mMIljUQE+nrTJDbMGYiaxUYQEexnTdEiYgkFoAJSABIp2U6dT2f9npPO2WYJSSc4dT77FPzq5YOdU/BbxEVQo7ym4IuUZgpABaQAJOJeHA6DHUdOX1jfLOkEO49kn4IfGuBDsyoRtKoaQZvqkTSuHI6fj7rNREoLBaACUgAScX/Hz6SxNrN1yJyCf5Jz6XaXYwJ8vWgZV5Y21cvStkYkjSopEIm4MwWgAlIAEil90u0Oth44xZrE46zafZwVO49nW98s0NeblpmtQ2YLURi+Glgt4jYUgApIAUik9DMMg+2HT7P872Os2Gk+TpxNdzkmyM+bllXNFqI21SNpVEmBSKQkUwAqIAUgEc/jcBj8dfgUK/4+xoqdx1m5K3sgCnYGokjaVC9Lo0phmnovUoIoABWQApCIOBwG2w6dcrYOrdx1nOQcAlGramYgals9kgYVQxWIRCykAFRACkAicimHw2DrQTMQLd95jJU7j5FyydT7EH8fWlWNoG0NcwxR/RgFIpHipABUQApAInI1dofB1oMprNh5nOV/H2PVruyBqIy/D62qlaVt5qDq+hVD8dZ9iESKjAJQASkAiUhe2R0GWw6kuHSZXXpzxjIBPrSuWtbZQlQvRoFIpDApABWQApCIFFRWIMqaZbZq13FOpboGotAAH1pXi3TOMqsfE6o7VYsUgAJQASkAiUhhszsM/tx/MrOF6Dirdh3n9CWBKCzQl9YXDaquW6GMApFIHigAFZACkIgUtQy7gz/3X+gyW737RI6BKL7ahS6zOtEKRCJXogBUQApAIlLcMuwONmUGouV/H+OP3cc5k+a6dEd4UGYgqh5JmxqR1I5SIBK5mAJQASkAiYjV0u0ONu07ac4y22kGorOXBKKywX7EVyvrXLqjVpRWuxfPpgBUQApAIlLSpNsdbNx38qIWohPZFnctG+znHFCdFYhsNgUi8RwKQAWkACQiJV263cGGvSedY4hyCkSRwX7OZTva1oikRnkFIindFIAKSAFIRNxNWoaDDXuTnbPM/kg8zvl0h8sx5UL8iM9sHWpbPZIa5YMViKRUUQAqIAUgEXF3aRkO1u9NNhd33WW2EKVmXBqI/J2tQ22qR1K9nAKRuDcFoAJSABKR0iY1w876PRe6zNYkZg9EUWX8neOH2lQvSzUFInEzCkAFpAAkIqXd+XQ76/ckZ84yO0pCUjJplwSi6NCLA1EkVSODFIikRFMAKiAFIBHxNOfT7azbk+xcumNtUjJpdtdAVCE0wGWWWZwCkZQwCkAFpAAkIp7ufLqdtUnJLM/sMluXQyCKCQu4MMusejliywYqEImlFIAKSAFIRMTV+XQ7CUknzEHVO4+zds8J0u2uvz4qZgWiGuYss8oRCkRSvBSACkgBSETkys6lZQairBaiPcnZAlGl8EBnC1Gb6pHElg2yqFrxFApABaQAJCKSN2fTMkhITHYJRBkO118vlSMCXWaZVY5QIJLCpQBUQApAIiIFczYtgzWJJ5xLd2zYezJbIIotG0ibapHObrNK4YEWVSulhQJQASkAiYgUrjOpZiDKGlS9Ye9J7JcEoiplg5zdZW1rRBITpkAkeaMAVEAKQCIiRet0ViDKnHa/cV/2QBQXGUSbapHOO1VXCAuwqFpxFwpABaQAJCJSvE6nZrB693HnWmYb9yZzSR6iamSQMwy1qR5JdKgCkbhSACogBSAREWudOp/OH7svzDLbuO9ktkBUrVyws8ssvppaiEQBqMAUgERESpaU8+n8sfu4uXTH38f4c3/2QFQ1Mshl6Q4FIs+jAFRACkAiIiXbxYFoxc5jbMqhhejiQBRfvawGVXsABaACUgASEXEvuQ1E8dUiaVPD7DZTICp9FIAKSAFIRMS9pZxPZ81VxhBlzTJrU6Ms8dUiqaj7ELk9BaACUgASESldcjOoWoHI/SkAFZACkIhI6XbqfDp/JJ644rT7i2/M2Ka6ApE7UAAqIAUgERHPcmkg2pTDjRkvDkTx1bV0R0mkAFRACkAiIp7t4kC0cufxHO9UrbXMSh4FoAJSABIRkYudTs1wmWWmQFQyKQAVkAKQiIhcSW4DUXxWIKpelsoRQRZV6zkUgApIAUhERPIia3HXFVdY7b5yROBFd6pWICoKCkAFpAAkIiIFkddAFF+tLLFlFYgKSgGogBSARESkMJ3JIRBlXBKIKoUHOluH2lSPVCDKh7z8/vYqppou65133qFq1aoEBAQQHx/PqlWrrnh8cnIyw4cPJyYmBn9/f2rXrs28efMKdE4REZGiFOzvw7W1y/N/Xesy96H2rB9zEx/f15qHOtWgeZVwfLxs7Es+x5cJe/nnnA10eOVn2r/8E49/sZ7Zf+xhz/GzVl9CqWNpC9CsWbPo378/kydPJj4+ngkTJjB79my2bdtGVFRUtuPT0tJo3749UVFR/Otf/6JSpUokJiYSHh5OkyZN8nXOnKgFSEREitPFLUQrdx1n/Z7kHFuI4jNbh9pWj6RyRCA2m82iiksmt+kCi4+Pp1WrVrz99tsAOBwOYmNjGTFiBE899VS24ydPnsyrr77K1q1b8fX1LZRz5kQBSERErHQ27eIuMwWi3HKLAJSWlkZQUBBz5syhZ8+ezv0DBgwgOTmZb775JttrunfvTtmyZQkKCuKbb76hfPny3HPPPTz55JN4e3vn65wAqamppKamOp+npKQQGxurACQiIiVCngJR5tT72LKeF4jyEoB8iqmmbI4ePYrdbic6Otplf3R0NFu3bs3xNTt37uSnn36ib9++zJs3jx07dvDQQw+Rnp7OmDFj8nVOgPHjxzNu3LiCX5SIiEgRCPLzoUOt8nSoVR4wA1FCYrJzUPX6vcnsSz7H3IR9zE3YB0DFsICLpt17ZiC6EssCUH44HA6ioqJ477338Pb2pkWLFuzbt49XX32VMWPG5Pu8Tz/9NKNGjXI+z2oBEhERKYmC/Hy4plY5rqlVDsg5EO0/eZ65a/cxd60CUU4sC0DlypXD29ubQ4cOuew/dOgQFSpUyPE1MTEx+Pr64u3t7dxXr149Dh48SFpaWr7OCeDv74+/v38BrkZERMQ6lwaic2l2EpIuTLtftyd7IIpxBiJzHFGVskEeFYgsC0B+fn60aNGCxYsXO8frOBwOFi9ezMMPP5zja9q3b8+MGTNwOBx4eZkz+P/66y9iYmLw8/MDyPM5RURESptAP2/a1yxH+5qXD0QHTp7nq7X7+MpDA5Hl0+AHDBjAlClTaN26NRMmTOCLL75g69atREdH079/fypVqsT48eMB2LNnDw0aNGDAgAGMGDGC7du3c9999/HII4/wzDPP5OqcuaFZYCIiUppdHIhW7jzO2j0nSLe7xoEKoQHOMNSmeiRxkSU/ELnFIGiAXr16ceTIEUaPHs3Bgwdp2rQp8+fPdwaVpKQkZ0sPQGxsLAsWLOCxxx6jcePGVKpUiUcffZQnn3wy1+cUERHxdDm1EK1NujDLbO2eExxMOc/X6/bz9br9gHsGoivRUhg5UAuQiIh4MpdAtOs465KSSbM7XI7JCkTxmYGoagkIRG5xH6CSTAFIRETkgvPpWV1mx80xRDkEouhQf5dZZlYEIgWgAlIAEhERubySGogUgApIAUhERCT3zqfbWZt04T5Ea3MIRFFlLg5EZalWLrjQA5ECUAEpAImIiORfbgJR71axvHxH40J9X7eZBSYiIiKlT4CvN21rRNK2RiTgGohW7jpGQlIyDSqFWVqjApCIiIgUqZwCkcPiDigFIBERESlWAb7eVz+oiHld/RARERGR0kUBSERERDyOApCIiIh4HAUgERER8TgKQCIiIuJxFIBERETE4ygAiYiIiMdRABIRERGPowAkIiIiHkcBSERERDyOApCIiIh4HAUgERER8TgKQCIiIuJxtBp8DgzDACAlJcXiSkRERCS3sn5vZ/0evxIFoBycOnUKgNjYWIsrERERkbw6deoUYWFhVzzGZuQmJnkYh8PB/v37KVOmDDabrVDPnZKSQmxsLHv27CE0NLRQz10S6PrcX2m/Rl2f+yvt16jryz/DMDh16hQVK1bEy+vKo3zUApQDLy8vKleuXKTvERoaWiq/sbPo+txfab9GXZ/7K+3XqOvLn6u1/GTRIGgRERHxOApAIiIi4nEUgIqZv78/Y8aMwd/f3+pSioSuz/2V9mvU9bm/0n6Nur7ioUHQIiIi4nHUAiQiIiIeRwFIREREPI4CkIiIiHgcBSARERHxOApAReCdd96hatWqBAQEEB8fz6pVq654/OzZs6lbty4BAQE0atSIefPmFVOl+ZOX65s+fTo2m83lERAQUIzV5s2vv/5Kjx49qFixIjabja+//vqqr1myZAnNmzfH39+fmjVrMn369CKvM7/yen1LlizJ9vnZbDYOHjxYPAXn0fjx42nVqhVlypQhKiqKnj17sm3btqu+zl1+BvNzfe72Mzhp0iQaN27svEle27Zt+eGHH674Gnf5/CDv1+dun9+lXn75ZWw2GyNHjrzicVZ8hgpAhWzWrFmMGjWKMWPGkJCQQJMmTejSpQuHDx/O8fhly5bRp08fBg8ezNq1a+nZsyc9e/Zk06ZNxVx57uT1+sC82+eBAwecj8TExGKsOG/OnDlDkyZNeOedd3J1/K5du7j55pu57rrrWLduHSNHjmTIkCEsWLCgiCvNn7xeX5Zt27a5fIZRUVFFVGHB/PLLLwwfPpwVK1awcOFC0tPTuemmmzhz5sxlX+NOP4P5uT5wr5/BypUr8/LLL7NmzRr++OMPrr/+em699Vb+/PPPHI93p88P8n594F6f38VWr17NlClTaNy48RWPs+wzNKRQtW7d2hg+fLjzud1uNypWrGiMHz8+x+Pvvvtu4+abb3bZFx8fbzzwwANFWmd+5fX6PvzwQyMsLKyYqitcgPHVV19d8Zj/+7//Mxo0aOCyr1evXkaXLl2KsLLCkZvr+/nnnw3AOHHiRLHUVNgOHz5sAMYvv/xy2WPc7WfwYrm5Pnf+GcwSERFhvP/++zl+zZ0/vyxXuj53/fxOnTpl1KpVy1i4cKHRsWNH49FHH73ssVZ9hmoBKkRpaWmsWbOGzp07O/d5eXnRuXNnli9fnuNrli9f7nI8QJcuXS57vJXyc30Ap0+fJi4ujtjY2Kv+T8fduNPnVxBNmzYlJiaGG2+8kaVLl1pdTq6dPHkSgLJly172GHf+DHNzfeC+P4N2u52ZM2dy5swZ2rZtm+Mx7vz55eb6wD0/v+HDh3PzzTdn+2xyYtVnqABUiI4ePYrdbic6Otplf3R09GXHTBw8eDBPx1spP9dXp04dPvjgA7755hs+/fRTHA4H7dq1Y+/evcVRcpG73OeXkpLCuXPnLKqq8MTExDB58mS+/PJLvvzyS2JjY+nUqRMJCQlWl3ZVDoeDkSNH0r59exo2bHjZ49zpZ/Biub0+d/wZ3LhxIyEhIfj7+/Pggw/y1VdfUb9+/RyPdcfPLy/X546f38yZM0lISGD8+PG5Ot6qz1CrwUuRatu2rcv/bNq1a0e9evWYMmUKL7zwgoWVSW7UqVOHOnXqOJ+3a9eOv//+mzfeeINPPvnEwsqubvjw4WzatInff//d6lKKRG6vzx1/BuvUqcO6des4efIkc+bMYcCAAfzyyy+XDQnuJi/X526f3549e3j00UdZuHBhiR+srQBUiMqVK4e3tzeHDh1y2X/o0CEqVKiQ42sqVKiQp+OtlJ/ru5Svry/NmjVjx44dRVFisbvc5xcaGkpgYKBFVRWt1q1bl/hQ8fDDD/Pdd9/x66+/Urly5Sse604/g1nycn2XcoefQT8/P2rWrAlAixYtWL16NW+++SZTpkzJdqw7fn55ub5LlfTPb82aNRw+fJjmzZs799ntdn799VfefvttUlNT8fb2dnmNVZ+husAKkZ+fHy1atGDx4sXOfQ6Hg8WLF1+2f7dt27YuxwMsXLjwiv3BVsnP9V3KbrezceNGYmJiiqrMYuVOn19hWbduXYn9/AzD4OGHH+arr77ip59+olq1ald9jTt9hvm5vku548+gw+EgNTU1x6+50+d3OVe6vkuV9M/vhhtuYOPGjaxbt875aNmyJX379mXdunXZwg9Y+BkW6RBrDzRz5kzD39/fmD59urF582Zj6NChRnh4uHHw4EHDMAyjX79+xlNPPeU8funSpYaPj4/x2muvGVu2bDHGjBlj+Pr6Ghs3brTqEq4or9c3btw4Y8GCBcbff/9trFmzxujdu7cREBBg/Pnnn1ZdwhWdOnXKWLt2rbF27VoDMF5//XVj7dq1RmJiomEYhvHUU08Z/fr1cx6/c+dOIygoyPjnP/9pbNmyxXjnnXcMb29vY/78+VZdwhXl9freeOMN4+uvvza2b99ubNy40Xj00UcNLy8vY9GiRVZdwhUNGzbMCAsLM5YsWWIcOHDA+Th79qzzGHf+GczP9bnbz+BTTz1l/PLLL8auXbuMDRs2GE899ZRhs9mMH3/80TAM9/78DCPv1+dun19OLp0FVlI+QwWgIjBx4kSjSpUqhp+fn9G6dWtjxYoVzq917NjRGDBggMvxX3zxhVG7dm3Dz8/PaNCggfH9998Xc8V5k5frGzlypPPY6Ohoo3v37kZCQoIFVedO1rTvSx9Z1zRgwACjY8eO2V7TtGlTw8/Pz6hevbrx4YcfFnvduZXX6/vPf/5j1KhRwwgICDDKli1rdOrUyfjpp5+sKT4Xcro2wOUzceefwfxcn7v9DN53331GXFyc4efnZ5QvX9644YYbnOHAMNz78zOMvF+fu31+Obk0AJWUz9BmGIZRtG1MIiIiIiWLxgCJiIiIx1EAEhEREY+jACQiIiIeRwFIREREPI4CkIiIiHgcBSARERHxOApAIiIi4nEUgERELsNms/H1119bXYaIFAEFIBEpkQYOHIjNZsv26Nq1q9WliUgpoNXgRaTE6tq1Kx9++KHLPn9/f4uqEZHSRC1AIlJi+fv7U6FCBZdHREQEYHZPTZo0iW7duhEYGEj16tWZM2eOy+s3btzI9ddfT2BgIJGRkQwdOpTTp0+7HPPBBx/QoEED/P39iYmJ4eGHH3b5+tGjR7ntttsICgqiVq1afPvtt86vnThxgr59+1K+fHkCAwOpVatWtsAmIiWTApCIuK3nnnuOO+64g/Xr19O3b1969+7Nli1bADhz5gxdunQhIiKC1atXM3v2bBYtWuQScCZNmsTw4cMZOnQoGzdu5Ntvv6VmzZou7zFu3DjuvvtuNmzYQPfu3enbty/Hjx93vv/mzZv54Ycf2LJlC5MmTaJcuXLF9xcgIvlX5Mutiojkw4ABAwxvb28jODjY5fHvf//bMAxzZfQHH3zQ5TXx8fHGsGHDDMMwjPfee8+IiIgwTp8+7fz6999/b3h5eRkHDx40DMMwKlasaDzzzDOXrQEwnn32Wefz06dPG4Dxww8/GIZhGD169DAGDRpUOBcsIsVKY4BEpMS67rrrmDRpksu+smXLOrfbtm3r8rW2bduybt06ALZs2UKTJk0IDg52fr19+/Y4HA62bduGzWZj//793HDDDVesoXHjxs7t4OBgQkNDOXz4MADDhg3jjjvuICEhgZtuuomePXvSrl27fF2riBQvBSARKbGCg4OzdUkVlsDAwFwd5+vr6/LcZrPhcDgA6NatG4mJicybN4+FCxdyww03MHz4cF577bVCr1dECpfGAImI21qxYkW25/Xq1QOgXr16rF+/njNnzji/vnTpUry8vKhTpw5lypShatWqLF68uEA1lC9fngEDBvDpp58yYcIE3nvvvQKdT0SKh1qARKTESk1N5eDBgy77fHx8nAONZ8+eTcuWLbnmmmv47LPPWLVqFdOmTQOgb9++jBkzhgEDBjB27FiOHDnCiBEj6NevH9HR0QCMHTuWBx98kKioKLp168apU6dYunQpI0aMyFV9o0ePpkWLFjRo0IDU1FS+++47ZwATkZJNAUhESqz58+cTExPjsq9OnTps3boVMGdozZw5k4ceeoiYmBg+//xz6tevD0BQUBALFizg0UcfpVWrVgQFBXHHHXfw+uuvO881YMAAzp8/zxtvvMETTzxBuXLluPPOO3Ndn5+fH08//TS7d+8mMDCQDh06MHPmzEK4chEpajbDMAyrixARySubzcZXX31Fz549rS5FRNyQxgCJiIiIx1EAEhEREY+jMUAi4pbUey8iBaEWIBEREfE4CkAiIiLicRSARERExOMoAImIiIjHUQASERERj6MAJCIiIh5HAUhEREQ8jgKQiIiIeBwFIBEREfE4/w9wU8BXIPqzDAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Plot training accuracy and validation accuracy\n", "plt.plot(history.history['accuracy'], label='Train Accuracy')\n", "plt.plot(history.history['val_accuracy'], label='Val Accuracy')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Accuracy')\n", "plt.legend()\n", "plt.title('Training and Validation Accuracy')\n", "plt.show()\n", "\n", "# Plot training loss and validation loss\n", "plt.plot(history.history['loss'], label='Train Loss')\n", "plt.plot(history.history['val_loss'], label='Val Loss')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.title('Training and Validation Loss')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 735}, "id": "5Z7cLAJxn_O4", "outputId": "d01b5591-885e-4ed2-8353-86986889070b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[1m23/23\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m136s\u001b[0m 6s/step\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Predict on validation data\n", "Y_pred = model.predict(val_generator)\n", "y_pred = np.argmax(Y_pred, axis=1)\n", "\n", "# Get true labels\n", "Y_true = val_generator.classes\n", "\n", "# Compute the confusion matrix\n", "cm = confusion_matrix(Y_true, y_pred)\n", "\n", "# Plot the confusion matrix using seaborn\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(cm, annot=True, fmt=\"d\", cmap=\"Blues\",\n", "            xticklabels=list(train_generator.class_indices.keys()),\n", "            yticklabels=list(train_generator.class_indices.keys()))\n", "plt.xlabel(\"Predicted Labels\")\n", "plt.ylabel(\"True Labels\")\n", "plt.title(\"Confusion Matrix\")\n", "plt.show()\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}