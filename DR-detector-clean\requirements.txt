# Core ML and Deep Learning
tensorflow==2.16.1
keras==3.1.1
numpy==1.26.4

# Computer Vision and Image Processing
opencv-python==4.10.0.84
pillow==10.3.0

# Web Framework and Dependencies
flask==3.0.3
werkzeug==3.0.2

# Data Science and Visualization (for training notebook)
matplotlib==3.8.4
seaborn==0.13.2
scikit-learn==1.4.2
pandas==2.2.2

# EfficientNet (if used in training)
efficientnet==1.1.1

# Additional TensorFlow dependencies
h5py==3.11.0
