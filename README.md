# 🩺 Diabetic Retinopathy Detection System

An AI-powered web application for automated detection and classification of diabetic retinopathy from fundus images using deep learning and explainable AI techniques.

## 🎯 Overview

Diabetic retinopathy is a serious eye condition that can lead to blindness if not detected early. This system helps medical professionals by:

- **Automated Analysis**: Classifies diabetic retinopathy into 5 stages
- **Visual Explanations**: Uses GradCAM to show which image regions influenced the AI's decision
- **Web Interface**: User-friendly interface for easy image upload and result visualization
- **High Accuracy**: Deep learning model trained on fundus image datasets

## 🏥 Classification Stages

| Stage | Description | Clinical Significance |
|-------|-------------|----------------------|
| **No DR** | No signs of diabetic retinopathy | Normal, continue monitoring |
| **Mild** | Microaneurysms only | Early stage, lifestyle changes |
| **Moderate** | More extensive retinal changes | Requires closer monitoring |
| **Severe** | Significant retinal damage | Needs immediate medical attention |
| **Proliferative DR** | New blood vessel growth | Emergency treatment required |

## 🚀 Features

- **🔍 AI Classification**: Automated 5-stage diabetic retinopathy classification
- **🎨 GradCAM Visualization**: Visual explanations of AI decision-making
- **🌐 Web Interface**: Clean, professional medical interface
- **🔒 Secure Upload**: File validation and secure filename handling
- **📊 Confidence Scores**: Prediction confidence for clinical decision support
- **📱 Responsive Design**: Works on desktop and mobile devices
- **⚡ Real-time Processing**: Fast inference with optimized model
- **🛡️ Error Handling**: Robust error handling and user feedback

## 📸 Screenshots

### Web Interface
![Web Interface](https://via.placeholder.com/800x400/0066cc/ffffff?text=Upload+Your+Fundus+Image)

### Results with GradCAM
![GradCAM Results](https://via.placeholder.com/800x400/cc6600/ffffff?text=AI+Prediction+%2B+Visual+Explanation)

*Note: Replace placeholder images with actual screenshots of your application*

## 🛠️ Technology Stack

- **Backend**: Python, Flask, TensorFlow/Keras
- **Frontend**: HTML5, Bootstrap 5, CSS3
- **AI/ML**: Convolutional Neural Networks, GradCAM
- **Image Processing**: OpenCV, PIL
- **Data Science**: NumPy, Pandas, Matplotlib

## 📋 Requirements

See `requirements.txt` for complete list. Key dependencies:

```
tensorflow==2.16.1
keras==3.1.1
flask==3.0.3
opencv-python==*********
pillow==10.3.0
numpy==1.26.4
```

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/diabetic-retinopathy-detection.git
   cd diabetic-retinopathy-detection
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Download the model**
   - Due to file size limitations, the trained model (`retina_weights.hdf5`) is not included
   - Train your own model using the provided Jupyter notebook
   - Or contact the repository owner for the pre-trained model

## 🚀 Usage

### Web Application

1. **Start the Flask server**
   ```bash
   python app.py
   ```

2. **Open your browser**
   - Navigate to `http://localhost:5000`

3. **Upload and Analyze**
   - Upload a fundus image (PNG, JPG, JPEG supported)
   - View the prediction result and confidence score
   - Examine the GradCAM visualization

### Command Line Inference

```bash
python inference.py
```
(Update the image path in the script)

## 📁 Project Structure

```
diabetic-retinopathy-detection/
├── app.py                 # Main Flask web application
├── gradcam.py            # GradCAM implementation for explainable AI
├── inference.py          # Standalone inference script
├── requirements.txt      # Python dependencies
├── train_fundusimg.ipynb # Model training notebook
├── templates/
│   └── index.html       # Web interface template
├── static/
│   └── bg/             # Background images
└── README.md           # This file
```

## 📊 Dataset

The model was trained on a comprehensive diabetic retinopathy dataset containing fundus images labeled with different stages of the condition.

### Dataset Information:
- **Source**: [Add your dataset link here - e.g., Kaggle, medical institution, etc.]
- **Size**: [Number of images - e.g., 35,126 images]
- **Classes**: 5 stages (No DR, Mild, Moderate, Severe, Proliferative DR)
- **Image Format**: High-resolution fundus photographs
- **Split**: Training/Validation/Test sets

### Dataset Used for training :
- **APTOS 2019 Blindness Detection**: [Kaggle Competition Dataset](https://www.kaggle.com/c/aptos2019-blindness-detection)


*Note: Please ensure you have proper permissions and follow ethical guidelines when using medical datasets.*

## 🧠 Model Architecture

- **Base Architecture**: Convolutional Neural Network (CNN)
- **Input Size**: 256x256 pixels
- **Output**: 5-class classification (softmax)
- **Training**: Transfer learning with data augmentation
- **Explainability**: GradCAM for visual explanations

## 🔬 Training Your Own Model

Use the provided Jupyter notebook (`train_fundusimg.ipynb`) to:

1. Prepare your dataset
2. Configure model architecture
3. Train with data augmentation
4. Evaluate performance
5. Save the trained model

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## ⚠️ Medical Disclaimer

This system is designed to assist medical professionals and should not be used as the sole basis for medical decisions. Always consult with qualified healthcare providers for proper diagnosis and treatment.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Medical datasets used for training
- TensorFlow and Keras communities
- Bootstrap for UI components
- OpenCV for image processing

## 📞 Contact

For questions, suggestions, or collaboration opportunities, please open an issue or contact the repository maintainer.


