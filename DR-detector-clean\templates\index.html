<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diabetic Retinopathy Detection</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-top: 2rem;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            color: #2c3e50;
        }
        .upload-section {
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            background: #f8f9fa;
        }
        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
        }
        .result-section {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            border-left: 5px solid #27ae60;
        }
        .image-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 2rem;
        }
        .image-box {
            text-align: center;
            flex: 1;
            min-width: 300px;
        }
        .image-box img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .stage-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="display-4">🩺 Diabetic Retinopathy Detection</h1>
            <p class="lead">AI-powered analysis of fundus images for early detection</p>
        </div>

        <div class="upload-section">
            <form action="/predict" method="post" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="file" class="form-label">
                        <i class="fas fa-upload"></i> Upload Fundus Image
                    </label>
                    <input type="file" class="form-control" id="file" name="file" accept="image/*" required>
                </div>
                <button type="submit" class="btn btn-primary btn-lg">
                    🔍 Analyze Image
                </button>
            </form>
        </div>

        {% if prediction %}
        <div class="result-section">
            <h3>📊 Analysis Results</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4>Prediction: <span class="text-primary">{{ prediction }}</span></h4>
                    {% if confidence %}
                    <p><strong>Confidence:</strong> {{ confidence }}</p>
                    {% endif %}
                </div>
            </div>

            {% if image_url and gradcam_image_url %}
            <div class="image-container">
                <div class="image-box">
                    <h5>Original Image</h5>
                    <img src="{{ image_url }}" alt="Original fundus image" class="img-fluid">
                </div>
                <div class="image-box">
                    <h5>AI Focus Areas (GradCAM)</h5>
                    <img src="{{ gradcam_image_url }}" alt="GradCAM visualization" class="img-fluid">
                    <p class="mt-2 text-muted">Red/yellow areas show regions the AI focused on for diagnosis</p>
                </div>
            </div>
            {% endif %}

            <div class="stage-info">
                <h5>📋 Understanding the Results:</h5>
                <ul>
                    <li><strong>No DR:</strong> No signs of diabetic retinopathy detected</li>
                    <li><strong>Mild:</strong> Early stage - microaneurysms present</li>
                    <li><strong>Moderate:</strong> More extensive retinal changes</li>
                    <li><strong>Severe:</strong> Significant retinal damage requiring attention</li>
                    <li><strong>Proliferative DR:</strong> Most severe stage - immediate medical care needed</li>
                </ul>
                <p class="text-warning"><strong>⚠️ Medical Disclaimer:</strong> This tool is for assistance only. Always consult with qualified healthcare providers for proper diagnosis and treatment.</p>
            </div>
        </div>
        {% endif %}

        <div class="text-center mt-4">
            <p class="text-muted">Powered by Deep Learning & GradCAM Visualization</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
