import os
from flask import Flask, render_template, request, send_from_directory, url_for
from PIL import Image
import numpy as np
import cv2
from gradcam import GradCAM  # Adjust if gradcam.py is in a different directory
import tensorflow as tf  # Replace with PyTorch if needed

app = Flask(__name__)

# Path to save uploaded images
UPLOAD_FOLDER = 'static'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)  # Ensure the folder exists
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Load your ML model
model = tf.keras.models.load_model('diabetic_retinopathy_model.h5')  # Replace with your model's path

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    if 'file' not in request.files:
        return render_template('index.html', prediction="No file uploaded")

    file = request.files['file']
    if file.filename == '':
        return render_template('index.html', prediction="No file selected")

    try:
        # Save the uploaded image
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
        file.save(file_path)

        # Preprocess the image for your model
        image = Image.open(file_path)
        original_image_resized = image.resize((256, 256))  # Adjust size based on your model
        image_array = np.expand_dims(np.array(original_image_resized) / 255.0, axis=0)  # Normalize

        # Make a prediction
        prediction = model.predict(image_array)
        stage = np.argmax(prediction)  # Get index of highest predicted class
        confidence_score = np.max(prediction)  # Get confidence score for that class

        stages = ["No DR", "Mild", "Moderate", "Severe", "Proliferative DR"]
        result = stages[stage] if stage < len(stages) else "Unknown Stage"

        # Debugging output
        print(f"Prediction: {result}, Confidence: {confidence_score}")

        # Generate Grad-CAM image
        # Generate Grad-CAM heatmap
        grad_cam = GradCAM(model=model, class_idx=stage)
        heatmap = grad_cam.compute_heatmap(image_array)

# Resize the heatmap to match the original image
        heatmap = cv2.resize(heatmap, (original_image_resized.size[0], original_image_resized.size[1]))

# Normalize and apply color map
        heatmap = np.maximum(heatmap, 0)
        heatmap /= (np.max(heatmap) + 1e-8)  # Normalize between 0 and 1
        heatmap = np.uint8(255 * heatmap)  # Scale to 0-255
        heatmap = cv2.applyColorMap(heatmap, cv2.COLORMAP_JET)

# Convert original image to BGR (if necessary) and blend
        original_image_bgr = cv2.cvtColor(np.array(original_image_resized), cv2.COLOR_RGB2BGR)
        superimposed_img = cv2.addWeighted(original_image_bgr, 0.6, heatmap, 0.4, 0)

# Save the resulting image
        gradcam_image_path = os.path.join(app.config['UPLOAD_FOLDER'], 'gradcam_' + file.filename)
        cv2.imwrite(gradcam_image_path, superimposed_img)

        '''grad_cam = GradCAM(model=model, class_idx=stage)
        heatmap = grad_cam.compute_heatmap(image_array)

# Overlay heatmap on the original image
        heatmap = cv2.resize(heatmap, (original_image_resized.size[0], original_image_resized.size[1]))
        heatmap = np.uint8(255 * heatmap)
        heatmap = cv2.applyColorMap(heatmap, cv2.COLORMAP_JET)

        original_image_bgr = cv2.cvtColor(np.array(original_image_resized), cv2.COLOR_RGB2BGR)
        superimposed_img = cv2.addWeighted(original_image_bgr, 0.6, heatmap, 0.4, 0)

# Save Grad-CAM image in BGR format
        gradcam_image_path = os.path.join(app.config['UPLOAD_FOLDER'], 'gradcam_' + file.filename)
        cv2.imwrite(gradcam_image_path, superimposed_img)'''

        # Generate URLs for display
        image_url = url_for('uploaded_file', filename=file.filename)
        gradcam_image_url = url_for('uploaded_file', filename='gradcam_' + file.filename)

        return render_template('index.html', 
                               prediction=result, 
                               confidence=confidence_score,
                               image_url=image_url,
                               gradcam_image_url=gradcam_image_url)

    except Exception as e:
        print(f"Error: {str(e)}")  # Log error to console
        return render_template('index.html', prediction=f"Error: {str(e)}")


# Route to serve uploaded images
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    app.run(debug=True)
    
'''import os
from flask import Flask, render_template, request, send_from_directory, url_for
from PIL import Image
import numpy as np
import tensorflow as tf  # Replace with PyTorch if needed

app = Flask(__name__)

# Path to save uploaded images
UPLOAD_FOLDER = 'static'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)  # Ensure the folder exists
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Load your ML model
model = tf.keras.models.load_model('retina_weights.hdf5')  # Replace with your model's path

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    if 'file' not in request.files:
        return render_template('index.html', prediction="No file uploaded")

    file = request.files['file']
    if file.filename == '':
        return render_template('index.html', prediction="No file selected")

    try:
        # Save the uploaded image
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
        file.save(file_path)

        # Preprocess the image for the model
        image = Image.open(file_path)
        image = image.resize((256, 256))  # Adjust size based on your model
        image_array = np.array(image) / 255.0  # Normalize
        image_array = np.expand_dims(image_array, axis=0)  # Add batch dimension

        # Make a prediction
        prediction = model.predict(image_array)
        stage = np.argmax(prediction)  # Replace with your model's specific logic

        stages = ["No DR", "Mild", "Moderate", "Severe", "Proliferative DR"]
        result = stages[stage] if stage < len(stages) else "Unknown Stage"

        # Generate image URL for display
        image_url = url_for('uploaded_file', filename=file.filename)

        return render_template('index.html', prediction=result, image_url=image_url)

    except Exception as e:
        return render_template('index.html', prediction=f"Error: {str(e)}")

# Route to serve uploaded images
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    app.run(debug=True)'''
