import os
import uuid
from werkzeug.utils import secure_filename
from flask import Flask, render_template, request, send_from_directory, url_for
from PIL import Image
import numpy as np
import cv2
from gradcam import GradCAM  # Adjust if gradcam.py is in a different directory
import tensorflow as tf  # Replace with PyTorch if needed

app = Flask(__name__)

# Path to save uploaded images
UPLOAD_FOLDER = 'static'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)  # Ensure the folder exists
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Allowed file extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Load your ML model
model = tf.keras.models.load_model('retina_weights.hdf5')  # Replace with your model's path

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    if 'file' not in request.files:
        return render_template('index.html', prediction="No file uploaded")

    file = request.files['file']
    if file.filename == '':
        return render_template('index.html', prediction="No file selected")

    if not allowed_file(file.filename):
        return render_template('index.html', prediction="Invalid file type. Please upload an image file.")

    try:
        # Generate secure filename
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)

        # Preprocess the image for your model
        image = Image.open(file_path)
        original_image_resized = image.resize((256, 256))  # Adjust size based on your model
        image_array = np.expand_dims(np.array(original_image_resized) / 255.0, axis=0)  # Normalize

        # Make a prediction
        prediction = model.predict(image_array)
        stage = np.argmax(prediction)  # Get index of highest predicted class
        confidence_score = np.max(prediction)  # Get confidence score for that class

        stages = ["No DR", "Mild", "Moderate", "Severe", "Proliferative DR"]
        result = stages[stage] if stage < len(stages) else "Unknown Stage"

        # Debugging output
        print(f"Prediction: {result}, Confidence: {confidence_score}")

        # Generate Grad-CAM visualization
        grad_cam = GradCAM(model=model, class_idx=stage)
        heatmap = grad_cam.compute_heatmap(image_array)

        # Convert PIL image to numpy array for overlay
        original_image_array = np.array(original_image_resized)
        superimposed_img = grad_cam.overlay_heatmap(heatmap, original_image_array, alpha=0.4)

        # Save the resulting image
        gradcam_filename = f'gradcam_{unique_filename}'
        gradcam_image_path = os.path.join(app.config['UPLOAD_FOLDER'], gradcam_filename)
        cv2.imwrite(gradcam_image_path, superimposed_img)

        

        # Generate URLs for display
        image_url = url_for('uploaded_file', filename=file.filename)
        gradcam_image_url = url_for('uploaded_file', filename='gradcam_' + file.filename)

        return render_template('index.html', 
                               prediction=result, 
                               confidence=confidence_score,
                               image_url=image_url,
                               gradcam_image_url=gradcam_image_url)

    except Exception as e:
        print(f"Error: {str(e)}")  # Log error to console
        return render_template('index.html', prediction=f"Error: {str(e)}")


# Route to serve uploaded images
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    app.run(debug=True)
    
