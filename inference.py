from tensorflow.keras.models import load_model
import cv2
import numpy as np

# Load pre-trained model
try:
    model = load_model('retina_weights.hdf5')  # Ensure model file is in the same directory
    print("✅ Model loaded successfully!")
except Exception as e:
    print(f"❌ Error loading model: {e}")
    print("Please ensure 'retina_weights.hdf5' exists in the current directory.")
    print("See MODEL_DOWNLOAD.md for instructions on obtaining the model.")
    exit(1)

# Load and preprocess input image
image_path = 'test1.png'  # Replace with your image path
image = cv2.imread(image_path)

if image is None:
    print(f"❌ Error: Could not load image from '{image_path}'")
    print("Please ensure the image file exists and update the image_path variable.")
    exit(1)

original_image = image.copy()  # Keep a copy to display later
image = cv2.resize(image, (256, 256))  # Adjust size based on model input
image = image / 255.0  # Normalize pixel values
image = np.expand_dims(image, axis=0)  # Add batch dimension

# Run inference
predictions = model.predict(image)

# Map predictions to labels
labels = ['No DR', 'Mild', 'Moderate', 'Severe', 'Proliferative DR']
predicted_stage = labels[np.argmax(predictions)]

# Display prediction on the image
font = cv2.FONT_HERSHEY_SIMPLEX
font_scale = 0.5
color = (0, 255, 0)  # Green color
thickness = 2
text = f"Prediction: {predicted_stage}"
cv2.putText(original_image, text, (10, 30), font, font_scale, color, thickness)

# Show the image with the prediction
cv2.imshow("Diabetic Retinopathy Detection", original_image)

# Wait for a key press and close the window
cv2.waitKey(0)
cv2.destroyAllWindows()
