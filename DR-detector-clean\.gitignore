# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Virtual Environment
.venv
venv/
env/
ENV/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so

# Model files (too large for GitHub)
*.hdf5
*.h5
*.pkl
*.joblib

# Archives and large files
*.zip
*.tar.gz
*.rar
*.7z

# Uploaded files and generated content
static/uploads/
static/*test*.png
static/*test*.jpg
static/*test*.jpeg
static/gradcam_*
uploads/

# IDE and OS
.vscode/
.idea/
.DS_Store
desktop.ini
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints/

# Logs
*.log
logs/

# Large DLL files
*.dll
