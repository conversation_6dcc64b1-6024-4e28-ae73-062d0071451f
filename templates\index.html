<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diabetic Retinopathy Prediction</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- Custom CSS -->
    <style>
        body {
            background-image: url("{{ url_for('static', filename='bg/medical-equipment-with-copy-space.jpg') }}"); /* Replace with your image file name */
            background-size: cover; /* Cover the entire viewport */
            background-position: center; /* Center the background image */
            font-family: 'Arial', sans-serif;
            color: #333; /* Default text color */
        }
        .container {
            margin-top: 50px;
            padding: 20px;
            background-color: rgba(255, 255, 255, 0.8); /* Slightly transparent white background */
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }
        .header-text {
            font-weight: bold;
            font-size: 32px;
            color: #0056b3;
        }
        .sub-text {
            font-size: 18px;
            color: #6c757d;
        }
        .upload-btn {
            background-color: #007bff;
            border: none;
            color: white;
            font-size: 16px;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .result-card {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px solid #d1d1d1;
            border-radius: 10px;
        }
        .result-title {
            color: #28a745;
            font-weight: bold;
            font-size: 24px;
        }
        .uploaded-image {
            max-width: 50%;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container text-center">
        <h1 class="header-text">Diabetic Retinopathy Prediction</h1>
        <p class="sub-text">Upload a fundus image to predict the stage of diabetic retinopathy.</p>
        <form action="/predict" method="post" enctype="multipart/form-data" class="mt-4">
            <div class="mb-3">
                <input type="file" name="file" class="form-control" required>
            </div>
            <button type="submit" class="upload-btn">Predict stage</button>
        </form>

        <!-- Result Section -->
        {% if prediction and image_url and gradcam_image_url %}
        <div class="result-card text-center mt-4">
            <h4>Prediction Result:</h4>
            <p style="font-size: 24px; font-weight: bold;">{{ prediction }}</p>
            <p style="font-size: 18px;">Confidence Score: {{ confidence | round(2) }}</p>
    
            <h5>Uploaded Fundus Image:</h5>
            <img src="{{ image_url }}" alt="Uploaded Image" class="uploaded-image">
    
            <h5>Grad-CAM Result:</h5>
            <img src="{{ gradcam_image_url }}" alt="Grad-CAM Image" class="uploaded-image">
        </div>
        {% endif %}

    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
