# 🤖 Model Download Instructions

## Why is the model not included?

The trained model file (`retina_weights.hdf5`) is **58MB** in size, which exceeds GitHub's file size limits for regular repositories. Therefore, it's not included in this repository.

## How to get the model?

### Option 1: Train Your Own Model
Use the provided Jupyter notebook `train_fundusimg.ipynb` to train your own model:

1. Prepare your diabetic retinopathy dataset
2. Follow the training notebook step by step
3. The trained model will be saved as `retina_weights.hdf5`

### Option 2: Download Pre-trained Model
If you need the pre-trained model used in this project:

1. **Contact the repository owner** through GitHub issues
2. **Use cloud storage**: The model can be shared via Google Drive, Dropbox, or similar services
3. **Use Git LFS**: For advanced users, the model could be stored using Git Large File Storage

### Option 3: Use Alternative Storage
Consider these platforms for large model files:

- **Hugging Face Model Hub**: Upload and download models easily
- **Google Drive**: Share via public links
- **AWS S3**: For production deployments
- **GitHub Releases**: Attach large files to releases

## Model Specifications

- **File Name**: `retina_weights.hdf5`
- **Size**: ~58MB
- **Format**: HDF5 (Keras/TensorFlow format)
- **Input Shape**: (256, 256, 3)
- **Output Classes**: 5 (No DR, Mild, Moderate, Severe, Proliferative DR)
- **Architecture**: CNN-based (details in training notebook)

## Placing the Model

Once you have the model file:

1. Place `retina_weights.hdf5` in the **root directory** of the project
2. The file structure should look like:
   ```
   diabetic-retinopathy-detection/
   ├── retina_weights.hdf5  ← Place the model here
   ├── app.py
   ├── gradcam.py
   └── ...
   ```

## Verification

To verify the model is working correctly:

```bash
python -c "
import tensorflow as tf
model = tf.keras.models.load_model('retina_weights.hdf5')
print('✅ Model loaded successfully!')
print(f'Input shape: {model.input_shape}')
print(f'Output shape: {model.output_shape}')
"
```

## Alternative Model Formats

If you have the model in a different format:

- **`.h5` files**: Should work directly with TensorFlow/Keras
- **`.pb` files**: TensorFlow SavedModel format (may need code modifications)
- **`.tflite` files**: TensorFlow Lite format (for mobile deployment)

## Need Help?

If you encounter issues with the model:

1. **Check the file path** in `app.py` and `inference.py`
2. **Verify TensorFlow version** compatibility
3. **Open an issue** on GitHub with error details
4. **Check the training notebook** for model architecture details

---

**Note**: This project prioritizes code sharing and educational value. The model can be recreated using the provided training notebook and appropriate datasets.
