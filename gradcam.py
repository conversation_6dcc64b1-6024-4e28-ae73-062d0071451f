import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Model
import cv2

class GradCAM:
    def __init__(self, model, class_idx, layer_name=None):
        self.model = model
        self.class_idx = class_idx
        self.layer_name = layer_name or self.find_target_layer()

    def find_target_layer(self):
        for layer in reversed(self.model.layers):
            if isinstance(layer, tf.keras.layers.Conv2D):
                return layer.name
        raise ValueError("No convolutional layer found.")

    def compute_heatmap(self, image):
        grad_model = Model(inputs=self.model.inputs, outputs=[self.model.output[0], self.model.get_layer(self.layer_name).output])
        
        with tf.GradientTape() as tape:
            model_output, conv_output = grad_model(image)
            loss = model_output[:, self.class_idx]

        grads = tape.gradient(loss, conv_output)
        pooled_grads = tf.reduce_mean(grads, axis=(0, 1))
        heatmap = conv_output[0] @ pooled_grads[..., tf.newaxis]
        heatmap = tf.maximum(heatmap, 0)
        heatmap /= (tf.reduce_max(heatmap)+ 1e-8)

        return heatmap.numpy()

    def overlay_heatmap(self, heatmap, original_image, alpha=0.4):
        heatmap = cv2.resize(heatmap, (original_image.shape[1], original_image.shape[0]))
        heatmap = np.uint8(255 * heatmap)
        heatmap = cv2.applyColorMap(heatmap, cv2.COLORMAP_JET)
        if original_image.dtype != np.uint8:
            original_image = (original_image * 255).astype(np.uint8)
        superimposed_img = cv2.addWeighted(original_image, alpha, heatmap, 1 - alpha, 0)
        return superimposed_img
